import { useState, useEffect } from 'react';

export function useTableSettings(tableKey) {
  const [columnOrder,  setColumnOrder]  = useState([]);
  const [columnSizing, setColumnSizing] = useState({});

  useEffect(() => {
    try {
      setColumnOrder(
        JSON.parse(localStorage.getItem(`${tableKey}-order`)) || []
      );
      setColumnSizing(
        JSON.parse(localStorage.getItem(`${tableKey}-sizing`)) || {}
      );
    } catch {}
  }, [tableKey]);

  useEffect(() => {
    localStorage.setItem(
      `${tableKey}-order`, JSON.stringify(columnOrder)
    );
  }, [tableKey, columnOrder]);

  useEffect(() => {
    localStorage.setItem(
      `${tableKey}-sizing`, JSON.stringify(columnSizing)
    );
  }, [tableKey, columnSizing]);

  return { columnOrder, columnSizing, setColumnOrder, setColumnSizing };
}