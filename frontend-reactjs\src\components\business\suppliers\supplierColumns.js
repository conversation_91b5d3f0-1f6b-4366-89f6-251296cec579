import React from "react";
import { IconButton } from "@mui/material";
import { InfoOutlined } from "@mui/icons-material";
import { infoIconButtonStyles, primaryNameCellStyle } from "../../../styles/tableStyles";

export default function supplierColumns({
  handleOpenDetailPanel,
  expandedSupplier,
}) {
  return [
    {
      id: "info",
      header: "",
      enablePinning: true,
      size: 64,
      minSize: 64,
      maxSize: 64,
      Cell: ({ row }) => (
        <IconButton
          className="expand-row-arrow"
          onClick={e => {
            e.stopPropagation();
            handleOpenDetailPanel(row.original);
          }}
          aria-label="View Supplier Details"
          sx={infoIconButtonStyles(expandedSupplier?.id === row.original.id)}
          size="small"
        >
          <InfoOutlined fontSize="small" />
        </IconButton>
      ),
    },
    {
      accessorKey: "company_name",
      header: "Company Name",
      sortingFn: (rowA, rowB, columnId) => {
        // 1) Pull the raw values (strings) and trim whitespace
        const rawA = String(rowA.getValue(columnId)).trim();
        const rawB = String(rowB.getValue(columnId)).trim();
    
        // 2) Attempt to convert to numbers
        const numA = Number(rawA);
        const numB = Number(rawB);
    
        const aIsNum = !isNaN(numA);
        const bIsNum = !isNaN(numB);
    
        // 3) If both parse to valid numbers, do a numeric comparison
        if (aIsNum && bIsNum) {
          if (numA === numB) return 0;
          return numA > numB ? 1 : -1;
        }
    
        // 4) Otherwise, fall back to a case-insensitive string compare
        return rawA.localeCompare(rawB, undefined, { numeric: true, sensitivity: "base" });
      },
      enablePinning: true,
      size: 180,
      minSize: 180,
      maxSize: 180,
      Cell: ({ row }) => (
        <span
          style={primaryNameCellStyle}
          onClick={e => {
            e.stopPropagation();
            handleOpenDetailPanel(row.original);
          }}
          data-interactive="true"
        >
          {row.original.company_name}
        </span>
      ),
    },

    { accessorKey: "company_code", header: "Code" },
    { accessorKey: "notes", header: "Notes" },
    { accessorFn: (row) => row.profile?.payment_terms || "—", header: "Payment Terms" },
    { accessorFn: (row) => row.profile?.credit_limit ?? "—", header: "Credit Limit" },
    { accessorFn: (row) => row.profile?.credit_hold ? "Yes" : "No", header: "Credit Hold" },
    { accessorFn: (row) => row.profile?.sla_description ?? "—", header: "SLA Desc." },
    { accessorFn: (row) => row.profile?.effective_date ?? "—", header: "Effective Date" },
    { accessorFn: (row) => row.profile?.expiration_date ?? "—", header: "Expiration Date" },
    { accessorFn: (row) => (row.contacts?.find((c) => c.is_primary)?.name) || "—", header: "Primary Contact Name" },
    { accessorFn: (row) => (row.contacts?.find((c) => c.is_primary)?.email) || "—", header: "Primary Email" },
    { accessorFn: (row) => (row.contacts?.find((c) => c.is_primary)?.phone) || "—", header: "Primary Phone" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "billing")?.street_address) || "—", header: "Billing Street" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "billing")?.city) || "—", header: "Billing City" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "billing")?.state) || "—", header: "Billing State" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "billing")?.zipcode) || "—", header: "Billing Zip" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "billing")?.contact_email) || "—", header: "Billing Email" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "shipping")?.street_address) || "—", header: "Shipping Street" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "shipping")?.city) || "—", header: "Shipping City" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "shipping")?.state) || "—", header: "Shipping State" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "shipping")?.zipcode) || "—", header: "Shipping Zip" },
    { accessorFn: (row) => (row.addresses?.find((a) => a.type === "shipping")?.contact_email) || "—", header: "Shipping Email" },
    {
      accessorKey: "created_at",
      header: "Created At",
      Cell: ({ cell }) =>
        cell.getValue() ? new Date(cell.getValue()).toLocaleDateString("en-US") : "—",
    },
  ];
}
