from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

from schemas.suppliers.supplier_addresses import SupplierAddressCreate, SupplierAddressRead
from schemas.suppliers.supplier_contacts import SupplierContactBase, SupplierContactRead
from schemas.suppliers.supplier_documents import SupplierDocumentBase, SupplierDocumentRead
from schemas.suppliers.supplier_profile import SupplierProfileCreate, SupplierProfileRead

class SupplierBase(BaseModel):
    company_name: str = Field(..., min_length=1)
    company_code: str = Field(..., min_length=1)
    notes: Optional[str] = ""

    # NEW: Normalize strings for company_name, company_code, notes
    @validator('company_name', 'company_code', 'notes', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()

class SupplierCreate(SupplierBase):
    addresses: List[SupplierAddressCreate] = []
    contacts: List[SupplierContactBase] = []
    documents: List[SupplierDocumentBase] = []
    profile: Optional[SupplierProfileCreate] = None

class SupplierUpdate(BaseModel):
    company_name: Optional[str] = None
    company_code: Optional[str] = None
    notes: Optional[str] = None
    addresses: Optional[List[SupplierAddressCreate]] = None
    contacts: Optional[List[SupplierContactBase]] = None
    documents: Optional[List[SupplierDocumentBase]] = None
    profile: Optional[SupplierProfileCreate] = None

    class Config:
        from_attributes = True

class SupplierRead(SupplierBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    updated_by: Optional[str]
    addresses: List[SupplierAddressRead] = []
    contacts: List[SupplierContactRead] = []
    documents: List[SupplierDocumentRead] = []
    profile: Optional[SupplierProfileRead] = None

    class Config:
        from_attributes = True
