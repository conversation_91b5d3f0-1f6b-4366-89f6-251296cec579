import React, { useState, useEffect } from "react";
import API from "../../services/api";
import toast from "react-hot-toast";

export default function PermissionMatrix() {
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState({});
  const [modules, setModules] = useState([]);
  const [currentRole, setCurrentRole] = useState("");

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      const [roleRes, permRes, moduleRes, sessionRes] = await Promise.all([
        API.get("/auth/roles"),
        API.get("/auth/permissions"),
        API.get("/auth/modules"),
        API.get("/auth/validate"),
      ]);

      setRoles(roleRes.data);
      setModules(moduleRes.data);
      setCurrentRole(sessionRes.data.roles[0] || "");

      // build a nested lookup: permissions[role][module][action] = allowed
      const structured = {};
      permRes.data.forEach(({ role, module, action, allowed }) => {
        structured[role] ||= {};
        structured[role][module] ||= {};
        structured[role][module][action] = allowed;
      });
      setPermissions(structured);
    } catch (err) {
      toast.error("Failed to load permissions data.");
      console.error(err);
    }
  };

  const handleToggle = async (role, module, action) => {
    if (
      role === currentRole &&
      module === "Permissions" &&
      ["view", "update"].includes(action)
    ) {
      toast.error("Cannot change your own critical permissions.");
      return;
    }

    const current = permissions?.[role]?.[module]?.[action] ?? false;
    const updated = { ...permissions };
    updated[role] ||= {};
    updated[role][module] ||= {};
    updated[role][module][action] = !current;
    setPermissions(updated);

    try {
      await API.put("/auth/permissions", {
        role,
        module,
        action,
        allowed: !current,
      });
      globalNotify(`Updated ${role} / ${module} / ${action}`);
    } catch (err) {
      toast.error("Update failed");
      console.error(err);
    }
  };

  return (
    <div className="space-y-6">
      <div className="overflow-x-auto rounded-2xl border border-gray-700 bg-white/5 backdrop-blur-md shadow-md">
        <table className="min-w-full text-sm text-left text-gray-100">
          <thead className="bg-gray-900 text-gray-300 text-xs uppercase">
            <tr>
              <th className="px-4 py-3">Module</th>
              <th className="px-4 py-3">Action</th>
              {roles.map((role) => (
                <th key={role} className="px-4 py-3 text-center">
                  {role}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {modules.flatMap((mod) =>
              mod.actions.map((action, idx) => (
                <tr
                  key={`${mod.name}-${action}`}
                  className={idx % 2 === 0 ? "bg-gray-800" : "bg-gray-900"}
                >
                  {idx === 0 && (
                    <td
                      rowSpan={mod.actions.length}
                      className="px-4 py-3 font-semibold align-top"
                    >
                      {mod.name}
                    </td>
                  )}
                  <td className="px-4 py-3">{action}</td>
                  {roles.map((role) => {
                    const isCritical =
                      role === currentRole &&
                      mod.name === "Permissions" &&
                      ["view", "update"].includes(action);
                    return (
                      <td
                        key={`${role}-${mod.name}-${action}`}
                        className="px-4 py-3 text-center"
                      >
                        <input
                          type="checkbox"
                          className="accent-yellow-400 h-5 w-5"
                          checked={
                            permissions?.[role]?.[mod.name]?.[action] ?? false
                          }
                          disabled={isCritical}
                          onChange={() => handleToggle(role, mod.name, action)}
                        />
                      </td>
                    );
                  })}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
