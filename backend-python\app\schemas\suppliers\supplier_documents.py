from sqlmodel import Field
from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime

class SupplierDocumentCreate(BaseModel):
    filename: str
    file_url: Optional[str] = ""
    type: Optional[str] = "other"

    @validator('filename', 'file_url', 'type', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()

class SupplierDocumentBase(BaseModel):
    filename: str
    file_url: str
    type: Optional[str] = "other"

    @validator('filename', 'file_url', 'type', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()

class SupplierDocumentRead(SupplierDocumentBase):
    id: int
    uploaded_at: Optional[datetime] = None

    class Config:
        from_attributes = True
