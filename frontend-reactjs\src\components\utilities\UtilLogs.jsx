import React, { useEffect, useState } from "react";
import API from "../../services/api";
import toast from "react-hot-toast";

export default function UtilLogs() {
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    (async () => {
      try {
        const res = await API.get("/settings/logs");
        setLogs(res.data.logs);
      } catch (err) {
        toast.error("Failed to fetch logs.");
        console.error(err);
      }
    })();
  }, []);

  return (
    <div className="mt-10">
      <div className="bg-gray-900 border border-gray-700 rounded-xl p-4 max-h-[70vh] overflow-y-auto text-sm font-mono text-green-200 shadow-inner">
        {logs.length === 0 ? (
          <div className="text-gray-400">No recent logs.</div>
        ) : (
          logs.map((line, idx) => <pre key={idx}>{line.trim()}</pre>)
        )}
      </div>
    </div>
  );
}
