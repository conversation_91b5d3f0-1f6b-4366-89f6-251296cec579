import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../context/auth/AuthProvider";
import { usePermissions } from "../../context/permissions/PermissionsProvider";

export default function PermissionRoutes({ module, action = "view", children }) {
  const { loading: authLoading, isAuthenticated } = useAuth();
  const { perms, loaded: permsLoaded } = usePermissions();

  if (authLoading) {
    return <div className="text-white p-10">Checking authentication…</div>;
  } else if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  } else if (!permsLoaded) {
    return <div className="text-white p-10">Checking permissions…</div>;
 }

 const ok = perms.some(
    (p) => p.module === module && p.action === action && p.allowed
 );

 return ok ? children : <Navigate to="/forbidden" replace />;
}