import React from 'react';
import { usePaginationMode } from '../../context/pagination/PaginationProvider';
import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';

export default function PaginationToggle() {
  const { mode, setMode } = usePaginationMode();

  return (
    <FormControlLabel
      control={
        <Switch
          checked={mode === 'server'}
          onChange={e => setMode(e.target.checked ? 'server' : 'client')}
        />
      }
      label={mode === 'server' ? 'Server Pagination' : 'Client Pagination'}
    />
  );
}
