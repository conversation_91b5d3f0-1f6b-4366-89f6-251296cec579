import React, { createContext, useState, useEffect, useContext } from 'react';

const PaginationModeContext = createContext({
  mode: 'server',
  setMode: () => {}
});

export const PaginationModeProvider = ({ children }) => {
  const [mode, setMode] = useState(
    localStorage.getItem('paginationMode') || 'server'
  );

  useEffect(() => {
    localStorage.setItem('paginationMode', mode);
  }, [mode]);

  return (
    <PaginationModeContext.Provider value={{ mode, setMode }}>
      {children}
    </PaginationModeContext.Provider>
  );
};

export const usePaginationMode = () => useContext(PaginationModeContext);
