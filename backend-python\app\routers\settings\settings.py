from fastapi import APIRouter, Depends
import os
from routers.auth.auth import get_current_user
from utilities.logs.config import LOG_FILE 

router = APIRouter(prefix="/settings", tags=["Settings"])

@router.get("/logs")
async def get_logs(current_user=Depends(get_current_user)):
    if not os.path.exists(LOG_FILE):
        return {"logs": []}
    with open(LOG_FILE, "r") as f:
        lines = f.readlines()[-100:]
    logs = [line.strip() for line in lines]
    return {"logs": logs}

@router.get("/")
async def get_settings_data(current_user=Depends(get_current_user)):
    return {"status": "Successful"}
