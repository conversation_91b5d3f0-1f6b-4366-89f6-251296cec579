// src/components/business/suppliers/SupplierTable.jsx
import React, { useState, useMemo, useRef, useEffect } from 'react';
import EntityTable from '../../tables/EntityTable.jsx';
import supplierColumns from './supplierColumns.js';
import SupplierDetailOverlay from './SupplierDetailOverlay.jsx';
import CreateNewSupplierModal from './CreateNewSupplierModal.jsx';
import DuplicateDialog from './DuplicateDialog.jsx';
import MassUpdateModal from './MassUpdateModal.jsx';
import { useSupplierActions } from './useSupplierActions.js';
import { useSuppliers } from '../../../hooks/useSuppliers.js';
import { usePagination } from '../../../hooks/usePagination.js';
import { usePaginationMode } from '../../../context/pagination/PaginationProvider.jsx';
import { tableStyling, infoIconButtonStyles, primaryNameCellStyle } from "../../../styles/tableStyles";
import toast from 'react-hot-toast';

export default function SupplierTable() {
  const [openCreate, setOpenCreate] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [rowSelection, setRowSelection] = useState({});
  const [preview, setPreview] = useState(null);
  const [importFile, setImportFile] = useState(null);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [massUpdateOpen, setMassUpdateOpen] = useState(false);
  const [expandedSupplier, setExpandedSupplier] = useState(null);
  const [detailPanelRect, setDetailPanelRect] = useState(null);
  const fileInputRef = useRef();
  const tableContainerRef = useRef();

  const { mode: paginationMode } = usePaginationMode();
  const { pageIndex, pageSize, setPageIndex, setPageSize } = usePagination('supplierTable', {
    initialPage: 0,
    initialSize: 10,
  });
  const serverPagination = paginationMode === 'server';

  const { suppliers, totalCount, isLoading, fetchSuppliers } = useSuppliers({
    page: pageIndex + 1,
    perPage: pageSize,
    serverPagination,
  });

  const {
    handleImport,
    handleCommitImport,
    handleDelete,
    handleMassUpdate,
  } = useSupplierActions({
    setPreview,
    setImportFile,
    setShowDuplicateDialog,
    fetchSuppliers,
    setRowSelection,
    setOpenCreate,
    setEditingSupplier,
    toast,
  });

  const columns = useMemo(
    () =>
      supplierColumns({
        setEditingSupplier,
        setOpenCreate,
        handleOpenDetailPanel: (supplier) => {
          const rect = tableContainerRef.current?.getBoundingClientRect();
          if (rect) {
            setDetailPanelRect({
              top: rect.top,
              left: rect.left,
              width: rect.width,
              height: rect.height,
            });
          }
          setExpandedSupplier(supplier);
        },
        expandedSupplier,
        infoIconButtonStyles,
        primaryNameCellStyle,
      }),
    [expandedSupplier]
  );

  return (
    <EntityTable
      columns={columns}
      data={suppliers}
      isLoading={isLoading}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      onAdd={() => { setEditingSupplier(null); setOpenCreate(true); }}
      onDelete={handleDelete}
      onClearSelection={() => setRowSelection({})}
      onImport={() => fileInputRef.current?.click()}
      onExport={() => {}}
      onMassUpdate={() => setMassUpdateOpen(true)}
      modals={
        <>
          {openCreate && (
            <CreateNewSupplierModal
              open={openCreate}
              onClose={() => { setOpenCreate(false); setEditingSupplier(null); }}
              supplier={editingSupplier}
              onSuccess={() => { fetchSuppliers(); setOpenCreate(false); setEditingSupplier(null); }}
            />
          )}
          {showDuplicateDialog && (
            <DuplicateDialog
              open={showDuplicateDialog}
              preview={preview}
              importFile={importFile}
              setPreview={setPreview}
              onClose={() => setShowDuplicateDialog(false)}
              onCommit={handleCommitImport}
            />
          )}
          <MassUpdateModal
            open={massUpdateOpen}
            onClose={() => setMassUpdateOpen(false)}
            selectedCount={Object.keys(rowSelection).length}
            onApply={handleMassUpdate}
          />
        </>
      }
      detailPanel={
        expandedSupplier &&
        detailPanelRect && (
          <SupplierDetailOverlay
            rect={detailPanelRect}
            supplier={expandedSupplier}
            onClose={() => setExpandedSupplier(null)}
            onSuccess={fetchSuppliers}
          />
        )
      }
      manualPagination={serverPagination}
      pageIndex={pageIndex}
      pageSize={pageSize}
      rowCount={serverPagination ? totalCount : undefined}
      onPageChange={setPageIndex}
      onPageSizeChange={setPageSize}
      muiTableContainerProps={{ ref: tableContainerRef }}
    />
  );
}
