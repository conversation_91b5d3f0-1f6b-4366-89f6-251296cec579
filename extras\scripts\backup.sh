#!/bin/bash
set -e

echo "[1/3] Dumping PostgreSQL database..."
mkdir -p backup
docker exec reactjs-db-1 pg_dump -U admin db > ./extras/backup/db_backup_$(date +%F).sql

echo "[2/3] Creating full project zip backup..."

if command -v zip >/dev/null 2>&1; then
  echo "[2a] Using zip..."
  zip -r ./backup/application_backup.zip . \
    -x "node_modules/*" \
    -x "frontend-reactjs/node_modules/*" \
    -x "backup/*" \
    -x "*.zip" \
    -x "backend/app/utilities/logs/activity.log"
else
  echo "[2b] zip not found — falling back to PowerShell's Compress-Archive..."
  powershell.exe -NoLogo -NoProfile -Command "& { \
    $files = Get-ChildItem -LiteralPath . -Recurse | Where-Object { -not $_.PSIsContainer -and $_.Name -ne 'activity.log' }; \
    Compress-Archive -LiteralPath $files.FullName -DestinationPath './backup/application_backup' \
  }"
fi

echo "[3/3] Backup done."
ls -lh backup/
