{"name": "frontend", "version": "1.0.0", "private": true, "proxy": "https://localhost:8000", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.0.2", "@tanstack/react-virtual": "^3.13.10", "axios": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "export-to-csv": "^1.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.10.5", "lodash.isequal": "^4.5.0", "lucide-react": "^0.509.0", "material-react-table": "^3.2.1", "papaparse": "^5.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.0", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "tw-elements-react": "^1.0.0-alpha-end", "xlsx": "^0.18.5", "zip": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "shadcn-ui": "^0.9.5", "tailwind-scrollbar": "^3.0.4", "tailwindcss": "^3.4.1", "tailwindcss-filters": "^3.0.0"}}