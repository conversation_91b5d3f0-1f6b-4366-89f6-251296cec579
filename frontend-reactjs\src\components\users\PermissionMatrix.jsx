import React, { useEffect, useState, useMemo } from "react";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Checkbox,
  Button,
} from "@mui/material";
import API from "../../services/api";
import CircularProgress from "@mui/material/CircularProgress";

const pretty = (str) =>
  str.charAt(0).toUpperCase() + str.slice(1).replace(/([a-z])([A-Z])/g, "$1 $2");

export default function PermissionMatrix() {
  const [permissions, setPermissions] = useState([]);
  const [roles, setRoles] = useState([]);
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState("");
  const [dirty, setDirty] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    async function fetchAll() {
      setLoading(true);
      try {
        const [rolesRes, modulesRes, permsRes] = await Promise.all([
          API.get("/auth/roles"),
          API.get("/auth/modules"),
          API.get("/auth/permissions"),
        ]);
        setRoles(rolesRes.data);
        setModules(
          modulesRes.data.map((m) => ({
            name: m.name,
            actions: m.actions,
          }))
        );
        setPermissions(permsRes.data);
        if (!selectedRole) {
          const firstRoleWithPerms = permsRes.data.find((p) => p.allowed)?.role;
          setSelectedRole(firstRoleWithPerms || rolesRes.data[0]);
        }
      } catch {
        // toast.error("Failed to load permissions matrix");
      }
      setLoading(false);
    }
    fetchAll();
    // eslint-disable-next-line
  }, []);

  const allActions = useMemo(() => ["create", "delete", "update", "view"], []);

  const visibleModules = modules;

  const isAllowed = (mod, action) =>
    permissions.some(
      (p) =>
        p.role === selectedRole &&
        p.module === mod &&
        p.action === action &&
        p.allowed
    );

  const handleToggle = (mod, action) => {
    setPermissions((prev) => {
      const idx = prev.findIndex(
        (p) =>
          p.role === selectedRole &&
          p.module === mod &&
          p.action === action
      );
      if (idx !== -1) {
        const newPerms = [...prev];
        newPerms[idx] = {
          ...newPerms[idx],
          allowed: !newPerms[idx].allowed,
        };
        setDirty(true);
        return newPerms;
      }
      setDirty(true);
      return [
        ...prev,
        {
          role: selectedRole,
          module: mod,
          action,
          allowed: true,
        },
      ];
    });
  };

  const handleSave = async () => {
    setSaving(true);
    const permsForRole = permissions.filter((p) => p.role === selectedRole);
    try {
      await Promise.all(
        permsForRole.map((p) =>
          API.put("/auth/permissions", {
            role: p.role,
            module: p.module,
            action: p.action,
            allowed: p.allowed,
          })
        )
      );
      setDirty(false);
      const res = await API.get("/auth/permissions");
      setPermissions(res.data);
    } catch {
      // toast.error("Failed to save permissions");
    }
    setSaving(false);
  };

  if (loading) {
    return (
      <Box className="flex justify-center py-10">
        <CircularProgress color="secondary" />
      </Box>
    );
  }

  return (
    <Box sx={{ my: 6, pt: 4, borderRadius: 2, background: "#1e293b", boxShadow: 3, overflowX: "auto" }}>

      <Box sx={{ px: 2, mb: 2, display: "flex", gap: 2, alignItems: "center" }}>
        <FormControl autoComplete="off" size="small" sx={{ minWidth: 320 }}>
          <InputLabel sx={{ color: "white" }}>Role</InputLabel>
          <Select
            value={selectedRole}
            label="Role"
            onChange={(e) => setSelectedRole(e.target.value)}
            sx={{
              color: "white",
              ".MuiSvgIcon-root": { color: "white" },
              ".MuiSelect-select": { backgroundColor: "#1f2937" },
              ".MuiInputLabel-root": { color: "white" },
              ".Mui-focused .MuiInputLabel-root": { color: "white" },
            }}
            MenuProps={{
              PaperProps: { sx: { bgcolor: "#1f2937", color: "white" } },
              MenuListProps: {
                sx: {
                  "& .MuiMenuItem-root": { color: "white" },
                  "& .MuiMenuItem-root.Mui-selected": { bgcolor: "#374151" },
                  "& .MuiMenuItem-root:hover": { bgcolor: "#4b5563" },
                },
              },
            }}
          >
            {roles.map((r) => (
              <MenuItem key={r} value={r}>
                {pretty(r)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <TableContainer
        component={Paper}
        sx={{
          background: "#1e293b",
          borderRadius: 3,
          mt: 1,
          border: "2px solid #334155", // Main border
        }}
      >
        <Table size="small">
          <TableHead>
            <TableRow
              sx={{
                "& th": {
                  color: "#a5b4fc",
                  fontWeight: 600,
                  background: "#1e293b",
                  borderBottom: "2px solid #334155",
                  borderRight: "1px solid #334155",
                },
              }}
            >
              <TableCell
                sx={{
                  color: "#a5b4fc",
                  fontWeight: 600,
                  background: "#1e293b",
                  borderBottom: "2px solid #334155",
                  borderRight: "1px solid #334155",
                }}
              >
                Module
              </TableCell>
              {allActions.map((action) => (
                <TableCell
                  key={action}
                  align="center"
                  sx={{
                    color: "#a5b4fc",
                    background: "#1e293b",
                    fontWeight: 600,
                    borderBottom: "2px solid #334155",
                    borderRight: "1px solid #334155",
                  }}
                >
                  {pretty(action)}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleModules.map((mod, rowIdx) => (
              <TableRow
                key={mod.name}
                sx={{
                  "& td": {
                    color: "#e0e7ff",
                    borderBottom: "1px solid #334155",
                    borderRight: "1px solid #334155",
                    background: rowIdx % 2 === 0 ? "#20263a" : "#232940",
                  },
                  "&:last-child td": {
                    borderBottom: "none",
                  },
                }}
              >
                <TableCell sx={{ fontWeight: 500 }}>{pretty(mod.name)}</TableCell>
                {allActions.map((action) => (
                  <TableCell align="center" key={action}>
                    <Checkbox
                      checked={isAllowed(mod.name, action)}
                      onChange={() => handleToggle(mod.name, action)}
                      sx={{
                        color: "#fff",
                        "&.Mui-checked": {
                          color: "#4ade80",
                        },
                        p: 0,
                      }}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {dirty && (
        <Box className="flex justify-end mt-3 pr-4">
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={saving}
            sx={{ boxShadow: "none" }}
          >
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </Box>
      )}

      <Divider sx={{ bgcolor: "#334155", mt: 2 }} />
    </Box>
  );
}