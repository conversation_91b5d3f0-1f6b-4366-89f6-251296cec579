import { useState, useEffect, useCallback } from 'react';
import API from '../services/api';

/**
 * useSuppliers
 * - Always expects backend to return { data: [...], totalCount: N }
 * - Supports server/client pagination, filtering, sorting, and search.
 * - Defensive: never crashes if API misbehaves.
 *
 * @param {object} options - Hook options.
 * @param {number} options.page - Page index (1-based for API).
 * @param {number} options.perPage - Page size.
 * @param {boolean} options.serverPagination - If true, backend pages. If false, frontend slices.
 * @param {string} [options.sortBy] - Field to sort by.
 * @param {'asc'|'desc'} [options.sortOrder] - Sort order.
 * @param {string} [options.search] - Search string.
 * @param {object} [options.filters] - Additional filters as key/val pairs.
 */
export function useSuppliers({
  page = 1,
  perPage = 10,
  serverPagination = false,
  sortBy = "company_name",
  sortOrder = "asc",
  search = "",
  filters = {},
} = {}) {
  const [suppliers, setSuppliers] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Build API params based on options
  const validPage = Number.isInteger(page) && page > 0 ? page : 1;
  const validSize = Number.isInteger(perPage) && perPage > 0 ? perPage : 10;

  const params = {
    sort_by: sortBy,
    sort_order: sortOrder,
    ...(serverPagination && { page: validPage, page_size: validSize }),
    ...(search && { search }),
    ...filters,
  };

  const fetchSuppliers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await API.get('/suppliers', { params });
      const dataArr = Array.isArray(res.data?.data) ? res.data.data : [];
      const count = Number.isFinite(res.data?.totalCount) ? res.data.totalCount : dataArr.length;

      setSuppliers(dataArr);
      setTotalCount(count);
    } catch (err) {
      setError(err);
      setSuppliers([]);
      setTotalCount(0);
      console.error("Failed to fetch suppliers:", err);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line
  }, [page, perPage, serverPagination, sortBy, sortOrder, search, JSON.stringify(filters)]);

  useEffect(() => {
    fetchSuppliers();
  }, [fetchSuppliers]);

  return {
    suppliers,
    totalCount,
    isLoading,
    error,
    fetchSuppliers,
  };
}
