import React, { useState } from "react";
import AppRouter from "./routes/AppRouter";
import { Toaster, toast } from "react-hot-toast";
import { darkTheme } from "./styles/theme";
import { ThemeProvider } from "@mui/material/styles";
import NotificationDrawer from "./components/utilities/NotificationDrawer";
import API from "./services/api";

function App() {
  const [allNotifications, setAllNotifications] = useState([]);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const notify = (message, type = "success") => {
    toast.dismiss();
    toast[type](message);
    setAllNotifications((prev) => [
      ...prev,
      { message, type, timestamp: new Date() },
    ]);
  };
  window.globalNotify = notify;

  return (
    <ThemeProvider theme={darkTheme}>
      <div className="min-h-screen relative">
        <Toaster
          position="top-right"
          containerStyle={{ 
            top: "4rem",   
            right: "1rem",  
          }}
          toastOptions={{
            duration: 4000,
            style: {
              background: "#1e293b",
              color: "#e0e7ff",
              fontSize: "0.85rem",
              border: "1px solid #6366f1",
            },
          }}
        />

        {allNotifications.length > 0 && (
          <button
            onClick={() => setDrawerOpen(true)}
            className="fixed right-4 z-50 text-sm bg-purple-600 hover:bg-purple-800 text-white px-3 py-1 rounded shadow-lg"
          >
            View All Notifications
          </button>
        )}

        <NotificationDrawer
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          notifications={allNotifications}
        />

        <AppRouter />
      </div>
    </ThemeProvider>
  );
}

export default App;
