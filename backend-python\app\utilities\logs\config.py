import logging
from datetime import datetime
import os

# Resolve absolute path based on this file's location
BASE_DIR = os.path.dirname(os.path.abspath(__file__))  # /backend-python/app/utilities/logs/
LOG_DIR = os.path.join(BASE_DIR)  # stays in the same folder
LOG_FILE = os.path.join(LOG_DIR, "activity.log")

os.makedirs(LOG_DIR, exist_ok=True)

logger = logging.getLogger("app_logger")
logger.setLevel(logging.INFO)

handler = logging.FileHandler(LOG_FILE)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)

if not logger.handlers:
    logger.addHandler(handler)

def log_event(message: str):
    logger.info(message)
