./
    backend-python/
        .dockerignore
        .env
        Dockerfile
        requirements.txt
        app/
            .vscode/
            auth/
                __pycache__/
            config/
                ssl/
                    certs/
                __pycache__/
            database/
                migrations/
            models/
                auth/
                supplier/
                    __pycache__/
                users/
                    __pycache__/
                __pycache__/
            routers/
                auth/
                    __pycache__/
                business/
                    suppliers/
                        __pycache__/
                    __pycache__/
                permissions/
                    __pycache__/
                roles/
                    __pycache__/
                settings/
                    __pycache__/
                users/
                    __pycache__/
                __pycache__/
            schemas/
                auth/
                    __pycache__/
                suppliers/
                    __pycache__/
                users/
                    __pycache__/
                __pycache__/
            uploads/
                contracts/
                documents/
            utilities/
                logs/
                    __pycache__/
                seeds/
                    __pycache__/
                validation/
                __pycache__/
            __pycache__/
    extras/
        backup/
        examples/
            uploads/
        scripts/
    frontend-reactjs/
        .dockerignore
        .env
        Dockerfile
        package-lock.json
        package.json
        postcss.config.js
        tailwind.config.js
        config/
            ssl/
                certs/
        nginx/
        public/
        src/
            components/
                business/
                    suppliers/
                layout/
                users/
                utilities/
            config/
            context/
                auth/
                permissions/
            hooks/
            images/
            pages/
                admin/
                auth/
                business/
                    suppliers/
                operations/
                permissions/
                users/
                utilities/
            routes/
            services/
            theme/