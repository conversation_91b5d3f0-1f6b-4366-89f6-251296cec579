// TableToolbarActions.styles.js

// Navbar-inspired container and button styles
export const containerClassNames = (isMobile) =>
    isMobile
      ? 'sticky top-0 z-50 bg-gray-900 text-white py-3 w-full flex flex-col gap-4'
      : 'sticky top-0 z-50 bg-gray-900 text-white py-3 w-full flex flex-row items-center gap-4';
  
  export const containerSx = (isMobile) => ({
    width: '100%',
    alignItems: isMobile ? 'flex-start' : 'center',
    justifyContent: 'flex-start',
    flexDirection: isMobile ? 'column' : 'row',
    gap: 4,
  });
  
  // Inner wrapper for buttons spacing
  export const innerClassName = 'flex flex-row items-center gap-4 pl-[15px]';
  export const innerSx = { width: 'auto' };
  
  // Navbar button style
  export const buttonClass = 'px-4 py-2 text-sm font-medium text-white rounded-lg bg-gray-800 transition min-w-fit';
  export const disabledStyle = 'opacity-50 pointer-events-none';