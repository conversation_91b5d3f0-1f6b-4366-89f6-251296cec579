// src/components/tables/EntityTable.jsx
import React from 'react';
import { Box } from '@mui/material';
import { MaterialReactTable } from 'material-react-table';
import TableToolbarActions from './TableToolbarActions';
import {
  tableStyling,
  wrapperBoxProps,
  topToolbarProps,
  bottomToolbarProps,
  paginationProps as paginationStyleProps,
} from '../../styles/tableStyles';

export default function EntityTable({
  columns,
  data,
  isLoading,
  rowSelection,
  setRowSelection,
  onAdd,
  onDelete,
  onClearSelection,
  onImport,
  onExport,
  onMassUpdate,
  modals,
  detailPanel,
  manualPagination = false,
  pageIndex,
  pageSize,
  rowCount,
  onPageChange,
  onPageSizeChange,
  muiTableHeadProps,
  muiTableHeadCellProps,
  muiTableBodyProps,
  muiTableBodyCellProps,
  ...rest
}) {
  // Build MRT pagination props when server-side/manual mode is on
  const mrtPagination = manualPagination
    ? {
        manualPagination: true,
        rowCount,
        pageCount: Math.ceil(rowCount / pageSize),
        onPaginationChange: ({ pageIndex: p, pageSize: s }) => {
          onPageChange(p);
          onPageSizeChange(s);
        },
        state: { pagination: { pageIndex, pageSize } },
      }
    : {};

  return (
    <Box {...wrapperBoxProps}>
      <MaterialReactTable
        key={manualPagination ? 'server' : 'client'}
        columns={columns}
        data={data}
        enableRowSelection
        enableStickyHeader
        enableColumnOrdering
        enableColumnResizing
        columnResizeMode="onChange"
        enableDensityToggle={true}
        
        state={{ isLoading, rowSelection }}
        onRowSelectionChange={setRowSelection}

        // apply manual/server pagination config
        manualPagination={manualPagination}
        {...mrtPagination}

        initialState={{
          columnPinning: {
            left: ['mrt-row-select', 'info', 'company_name'],
          },
        }}

        // top toolbar actions
        renderTopToolbarCustomActions={() => (
          <TableToolbarActions
            onAdd={onAdd}
            onDelete={onDelete}
            onClearSelection={onClearSelection}
            selectedCount={Object.keys(rowSelection).length}
            onImport={onImport}
            onExport={onExport}
            onMassUpdate={onMassUpdate}
          />
        )}

        // dark theme styling
        muiTablePaperProps={tableStyling.muiTablePaperProps}
        muiTableContainerProps={tableStyling.muiTableContainerProps}

        // head & body cell styling
        muiTableHeadProps={muiTableHeadProps ?? tableStyling.muiTableHeadProps}
        muiTableHeadCellProps={muiTableHeadCellProps ?? tableStyling.muiTableHeadCellProps}
        muiTableBodyProps={muiTableBodyProps ?? tableStyling.muiTableBodyProps}
        muiTableBodyCellProps={muiTableBodyCellProps ?? tableStyling.muiTableBodyCellProps}

        // toolbar backgrounds
        muiTopToolbarProps={topToolbarProps}
        muiBottomToolbarProps={bottomToolbarProps}

        // pagination row styling (only styling, not logic)
        muiTablePaginationProps={paginationStyleProps}

        {...rest}
      />

      {modals}
      {detailPanel}
    </Box>
  );
}
