// TableToolbarActions.jsx
import React from 'react';
import { useMediaQuery, Box } from '@mui/material';
import { TERipple } from 'tw-elements-react';
import { useTheme } from '@mui/material/styles';
import {
  containerClassNames,
  containerSx,
  innerClassName,
  innerSx,
  buttonClass,
  disabledStyle,
} from '../tables/TableToolbarActions.styles.js';

export default function TableToolbarActions({
  onAdd,
  onDelete,
  onImport,
  onExport,
  onMassUpdate,
  onClearSelection,
  selectedCount = 0,
}) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box className={containerClassNames(isMobile)} sx={containerSx(isMobile)}>
      <Box className={innerClassName} sx={innerSx}>
        <TERipple>
          <button
            type="button"
            className={buttonClass}
            onClick={onAdd}
          >
            Add
          </button>
        </TERipple>
        <TERipple>
          <button
            type="button"
            className={`${buttonClass} ${selectedCount === 0 ? disabledStyle : ''}`}
            onClick={onMassUpdate}
            disabled={selectedCount === 0}
          >
            Update
          </button>
        </TERipple>
        <TERipple>
          <button
            type="button"
            className={`${buttonClass} ${selectedCount === 0 ? disabledStyle : ''}`}
            onClick={onDelete}
            disabled={selectedCount === 0}
          >
            Delete
          </button>
        </TERipple>
        <TERipple>
          <button
            type="button"
            className={`${buttonClass} ${selectedCount === 0 ? disabledStyle : ''}`}
            onClick={onClearSelection}
            disabled={selectedCount === 0}
          >
            Clear
          </button>
        </TERipple>
        <TERipple>
          <button
            type="button"
            className={buttonClass}
            onClick={onImport}
          >
            Import
          </button>
        </TERipple>
        <TERipple>
          <button
            type="button"
            className={buttonClass}
            onClick={onExport}
          >
            Export
          </button>
        </TERipple>
      </Box>
    </Box>
  );
}
