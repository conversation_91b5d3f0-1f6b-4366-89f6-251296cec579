from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import select
from sqlalchemy.orm import selectinload, Session
from typing import List
from pydantic import BaseModel

from database.database import get_db
from utilities.logs.config import log_event
from models.models import RolePermission, Module 
from routers.auth.auth import get_current_user
from schemas.users.users import ROLE_HIERARCHY

router = APIRouter(prefix="/auth", tags=["Permissions"])

# --- Pydantic Schemas ------------------------------

class PermissionOut(BaseModel):
    role: str
    module: str
    action: str
    allowed: bool

    model_config = {
        "from_attributes": True
    }

class PermissionUpdateItem(BaseModel):
    role: str
    module: str
    action: str
    allowed: bool

class ModuleOut(BaseModel):
    name: str
    actions: List[str]

    model_config = {
        "from_attributes": True
    }

# --- Permission‐guard factory ----------------------

def require_permission(module_name: str, action: str):
    def checker(
        current_user=Depends(get_current_user),
        db: Session = Depends(get_db),
    ):
        module_rec = db.exec(
            select(Module).where(Module.name == module_name)
        ).one_or_none()
        if not module_rec:
            raise HTTPException(404, f"Unknown module '{module_name}'")

        # Get string roles of the user
        role_names = [ur.role for ur in current_user.roles]

        perm = db.exec(
            select(RolePermission)
            .where(
                RolePermission.role.in_(role_names),
                RolePermission.module_id == module_rec.id,
                RolePermission.action == action,
                RolePermission.allowed.is_(True),
            )
        ).first()

        if not perm:
            raise HTTPException(403, f"Missing permission: {module_name}.{action}")
        return True

    return checker

def has_permission(user, module_name: str, action: str, db: Session) -> bool:
    module_rec = db.exec(
        select(Module).where(Module.name == module_name)
    ).one_or_none()
    if not module_rec:
        return False

    role_names = [ur.role for ur in user.roles]

    perm = db.exec(
        select(RolePermission)
        .where(
            RolePermission.role.in_(role_names),
            RolePermission.module_id == module_rec.id,
            RolePermission.action == action,
            RolePermission.allowed.is_(True),
        )
    ).first()

    return bool(perm)

@router.get(
    "/permissions",
    response_model=List[PermissionOut],
)
def get_permissions(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    # If current user has "admin" role, show all permissions
    role_names = [ur.role for ur in current_user.roles]
    if "admin" in role_names:
        records = db.exec(
            select(RolePermission)
            .options(
                selectinload(RolePermission.module),
            )
        ).all()
    else:
        # Otherwise, only their own
        records = db.exec(
            select(RolePermission)
            .where(
                RolePermission.role.in_(role_names),
                RolePermission.allowed.is_(True),
            )
            .options(
                selectinload(RolePermission.module),
            )
        ).all()

    return [
        PermissionOut(
            role=p.role,
            module=p.module.name,
            action=p.action,
            allowed=p.allowed,
        )
        for p in records
    ]

@router.put(
    "/permissions",
    response_model=PermissionOut,
    dependencies=[Depends(require_permission("Permissions", "update"))],
)
def update_permission(
    item: PermissionUpdateItem,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):

    my_power = max(ROLE_HIERARCHY.get(ur.role, 0) for ur in current_user.roles)
    target_power = ROLE_HIERARCHY.get(item.role, 0)
    if target_power >= my_power:
        raise HTTPException(403, "Cannot change permissions for that role")

    module_rec = db.exec(select(Module).where(Module.name == item.module)).first()
    if not module_rec:
        raise HTTPException(404, detail="Invalid module")

    perm = db.exec(
        select(RolePermission)
        .where(
            RolePermission.role == item.role,
            RolePermission.module_id == module_rec.id,
            RolePermission.action == item.action,
        )
    ).first()

    if perm:
        perm.allowed = item.allowed
    else:
        perm = RolePermission(
            role=item.role,
            module_id=module_rec.id,
            action=item.action,
            allowed=item.allowed,
        )
        db.add(perm)

    db.commit()

    log_event(
        f"[Permissions] {current_user.username} updated role '{item.role}': {item.module}.{item.action} → {item.allowed}"
    )

    db.refresh(perm)

    return PermissionOut(
        role=perm.role,
        module=module_rec.name,
        action=perm.action,
        allowed=perm.allowed,
    )

@router.get(
    "/modules",
    response_model=List[ModuleOut],
    dependencies=[Depends(require_permission("Permissions", "view"))],
)
def get_modules(db: Session = Depends(get_db)):
    """
    List all modules and, for each, the distinct actions present in role_permissions.
    """
    mods = db.exec(select(Module)).all()
    out: List[ModuleOut] = []
    for m in mods:
        actions = db.exec(
            select(RolePermission.action)
            .where(RolePermission.module_id == m.id)
            .distinct()
        ).all()
        actions = [a[0] if isinstance(a, tuple) else a for a in actions]
        out.append(ModuleOut(name=m.name, actions=actions))
    return out

@router.get("/roles", response_model=List[str])
def get_roles():
    # Just return a static list, or your ALLOWED_ROLES from your schema:
    from schemas.users.users import ALLOWED_ROLES
    return ALLOWED_ROLES

@router.get(
    "/permissions/{role}/{module}",
    response_model=List[PermissionOut],
    dependencies=[Depends(require_permission("Permissions", "view"))]
)
def get_role_permissions(
    role: str,
    module: str,
    db: Session = Depends(get_db),
):
    module_rec = db.exec(select(Module).where(Module.name == module)).first()
    if not module_rec:
        raise HTTPException(404, detail="Module not found")
    perms = db.exec(
        select(RolePermission)
        .where(
            RolePermission.role == role,
            RolePermission.module_id == module_rec.id
        )
    ).all()
    return [
        PermissionOut(
            role=role,
            module=module_rec.name,
            action=p.action,
            allowed=p.allowed,
        ) for p in perms
    ]
