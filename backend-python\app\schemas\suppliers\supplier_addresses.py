from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime

class SupplierAddressBase(BaseModel):
    type: str
    is_primary: bool = False
    street_address: Optional[str] = ""
    city: Optional[str] = ""
    state: Optional[str] = ""
    zipcode: Optional[str] = ""
    phone: Optional[str] = ""
    contact_email: Optional[str] = ""
    notes: Optional[str] = ""

    # Validator to clean all string fields
    @validator(
        'type', 'street_address', 'city', 'state', 'zipcode', 'phone',
        'contact_email', 'notes',
        pre=True, always=True
    )
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        # Remove trailing ".0" for zipcodes and phones (from Excel/pandas)
        val = str(v).strip()
        if val.endswith('.0'):
            val = val[:-2]
        return val

class SupplierAddressCreate(SupplierAddressBase):
    pass

class SupplierAddressRead(SupplierAddressBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
