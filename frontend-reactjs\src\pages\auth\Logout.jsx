import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import API from "../../services/api";
import toast from "react-hot-toast";

function Logout({ setIsAuthenticated }) {
  const navigate = useNavigate();

  useEffect(() => {
    const doLogout = async () => {
      try {
        await API.post("/auth/logout");
        setIsAuthenticated(false);
        globalNotify("Logged off");
        navigate("/login");
      } catch (error) {
        toast.error("Logout failed");
      }
    };

    doLogout();
  }, [navigate, setIsAuthenticated]);

  return (
    <div className="flex justify-center items-center h-screen">
      <p>Logging out...</p>
    </div>
  );
}

export default Logout;
