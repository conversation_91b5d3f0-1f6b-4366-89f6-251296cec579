from pydantic import BaseModel
from typing import List

class LoginRequest(BaseModel):
    identifier: str
    password: str

class ModuleOut(BaseModel):
    name: str
    actions: List[str]

    model_config = {
        "from_attributes": True
    }

class PermissionOut(BaseModel):
    role: str
    module: str
    action: str
    allowed: bool

    model_config = {
        "from_attributes": True
    }

class PermissionUpdate(BaseModel):
    role: str
    module: str
    action: str
    allowed: bool