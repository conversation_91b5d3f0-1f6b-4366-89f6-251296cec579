import React, { useEffect, useState, useMemo } from "react";
import { MaterialReactTable } from "material-react-table";
import {
  Box,
  Button,
  IconButton,
  Tooltip,
  Chip,
  MenuItem,
  Select,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Typography,
  OutlinedInput,
} from "@mui/material";
import { Edit, Delete, Replay } from "@mui/icons-material";
import VpnKeyIcon from "@mui/icons-material/VpnKey";
import toast from "react-hot-toast";
import API from "../../services/api";
import CreateNewUserModal from "./CreateNewUserModal";
import { tableStyling } from "./UserTable.styles";

const STORAGE_KEY_ORDER = "user_column_order";
const STORAGE_KEY_SIZING = "user_column_sizing";
const STORAGE_KEY_PAGE = "user_table_page";
const STORAGE_KEY_PAGE_SIZE = "user_table_page_size";

export default function UserTable({ users, onDelete, onCreated }) {
  // State hooks
  const [data, setData] = useState([]);
  const [availableRoles, setAvailableRoles] = useState([]);
  const [roleLabelMap, setRoleLabelMap] = useState({});
  const [rowEdits, setRowEdits] = useState({});
  const [rowSelection, setRowSelection] = useState({});
  const [bulkRoles, setBulkRoles] = useState([]);
  const [openCreate, setOpenCreate] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState({ open: false, userId: null, password: "" });
  const [resetDialog, setResetDialog] = useState({
    open: false,
    userId: null,
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [page, setPage] = useState(() => {
    try {
      const saved = JSON.parse(localStorage.getItem(STORAGE_KEY_PAGE));
      return Number.isInteger(saved) && saved >= 0 ? saved : 0;
    } catch {
      return 0;
    }
  });
  const [pageSize, setPageSize] = useState(() => {
    try {
      const saved = JSON.parse(localStorage.getItem(STORAGE_KEY_PAGE_SIZE));
      return Number.isInteger(saved) && saved > 0 ? saved : 10;
    } catch {
      return 10;
    }
  });
  const [columnOrder, setColumnOrder] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem(STORAGE_KEY_ORDER)) || [];
    } catch {
      return [];
    }
  });
  const [columnSizing, setColumnSizing] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem(STORAGE_KEY_SIZING)) || {};
    } catch {
      return {};
    }
  });

  // Effects
  useEffect(() => { setData(users); }, [users]);
  useEffect(() => {
    API.get("/auth/roles")
      .then((res) => {
        if (Array.isArray(res.data)) {
          const labelMap = {};
          res.data.forEach((r) => {
            labelMap[r] = r === "admin"
              ? "Administrator"
              : r.charAt(0).toUpperCase() + r.slice(1);
          });
          setAvailableRoles(res.data);
          setRoleLabelMap(labelMap);
        }
      })
      .catch(() => toast.error("Could not load roles"));
  }, []);

  // Persist pagination
  const handlePaginationChange = (updater) => {
    const next = typeof updater === "function"
      ? updater({ pageIndex: page, pageSize })
      : updater;
    setPage(next.pageIndex);
    setPageSize(next.pageSize);
    localStorage.setItem(STORAGE_KEY_PAGE, JSON.stringify(next.pageIndex));
    localStorage.setItem(STORAGE_KEY_PAGE_SIZE, JSON.stringify(next.pageSize));
  };

  // Persist column order & sizing
  const handleColumnOrderChange = (order) => {
    setColumnOrder(order);
    localStorage.setItem(STORAGE_KEY_ORDER, JSON.stringify(order));
  };
  const handleColumnSizingChange = (sizing) => {
    setColumnSizing(sizing);
    localStorage.setItem(STORAGE_KEY_SIZING, JSON.stringify(sizing));
  };

  // Columns (auto-stretch except for ID/Actions)
  const columns = useMemo(
    () => [
      {
        accessorKey: "id",
        header: "ID",
        size: 120,
        Cell: ({ row }) => (
          <Typography
            variant="body2"
            sx={{ color: "#818cf8", textDecoration: "underline", cursor: "pointer" }}
          >
            {row.original.id}
          </Typography>
        ),
      },
      {
        accessorKey: "username",
        header: "Username",
        // Let MRT auto-size
        Cell: ({ row }) => (
          <Typography
            variant="body2"
            sx={{ color: "#fff" }}
          >
            {row.original.username}
          </Typography>
        ),
      },
      {
        accessorKey: "roles",
        header: "Roles",
        size: 500,
        Cell: ({ row }) =>
          row.original.roles.map((role) => (
            <Chip
              key={role}
              label={roleLabelMap[role] || role}
              size="small"
              sx={{
                mr: 0.5,
                mb: 0.3,
                backgroundColor: "#334155",
                color: "#c7d2fe",
                fontWeight: 500,
                fontSize: 12,
                borderRadius: 2,
              }}
            />
          )),
      },
      {
        accessorKey: "last_login",
        header: "Last Login",
        Cell: ({ cell }) => cell.getValue() || "—",
      },
      {
        id: "actions",
        header: "Actions",
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <Tooltip title="Reset Password">
              <IconButton
                size="small"
                onClick={() =>
                  setResetDialog({
                    open: true,
                    userId: row.original.id,
                    currentPassword: "",
                    newPassword: "",
                    confirmNewPassword: "",
                  })
                }
              >
                <VpnKeyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete User">
              <IconButton
                color="error"
                size="small"
                onClick={() =>
                  setDeleteDialog({ open: true, userId: row.original.id, password: "" })
                }
              >
                <Delete fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [roleLabelMap]
  );

  // Handle individual row edit (multi-role)
  const handleApplyEdit = async (userId) => {
    const setting = rowEdits[userId];
    if (!setting || !setting.value.length) return;
    try {
      await API.put(`/users/${userId}`, { roles: setting.value });
      toast.success("Roles updated");
      setData((prev) =>
        prev.map((u) =>
          u.id === userId ? { ...u, roles: setting.value } : u
        )
      );
      setRowEdits((prev) => {
        const c = { ...prev };
        delete c[userId];
        return c;
      });
    } catch {
      toast.error("Failed to update roles");
    }
  };

  // Bulk role update
  const handleBulkUpdateRoles = async () => {
    const ids = Object.keys(rowSelection).map((id) => parseInt(id, 10));
    if (bulkRoles.length === 0 || ids.length === 0) {
      toast.error("Select roles and users first");
      return;
    }
    try {
      await API.put("/users/bulk", { user_ids: ids, roles: bulkRoles });
      toast.success("Bulk roles updated");
      setData((prev) =>
        prev.map((u) => (ids.includes(u.id) ? { ...u, roles: bulkRoles } : u))
      );
      setBulkRoles([]);
      setRowSelection({});
    } catch {
      toast.error("Bulk update failed");
    }
  };

  // Handle password reset
  const handleResetPassword = async () => {
    const { userId, currentPassword, newPassword, confirmNewPassword } = resetDialog;
    if (newPassword !== confirmNewPassword) {
      toast.error("New passwords do not match");
      return;
    }
    try {
      await API.put(`/users/${userId}`, {
        current_password: currentPassword,
        password: newPassword,
      });
      toast.success("Password reset");
      setResetDialog({ open: false, userId: null, currentPassword: "", newPassword: "", confirmNewPassword: "" });
    } catch (err) {
      let message = "Failed to reset password. Please check password policy.";
      if (err.response?.data?.detail) {
        if (Array.isArray(err.response.data.detail)) {
          // FastAPI validation errors: array of objects
          message = err.response.data.detail.map(
            (e) => `${e.msg}${e.loc ? ` [${e.loc.join(" > ")}]` : ""}`
          ).join("; ");
        } else if (typeof err.response.data.detail === "string") {
          message = err.response.data.detail;
        }
      }
      toast.error(message);
    }
  };

  // Handle delete user
  const confirmDelete = async () => {
    try {
      await API.delete(`/users/${deleteDialog.userId}`, {
        data: { password: deleteDialog.password },
      });
      toast.success("User deleted");
      onDelete(deleteDialog.userId);
      setDeleteDialog({ open: false, userId: null, password: "" });
    } catch (err) {
      toast.error(err.response?.data?.detail || "Delete failed");
    }
  };

  // Table body edit cell
  const renderEditRow = (row) => {
    const setting = rowEdits[row.id];
    if (!setting) return null;
    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 2, py: 1 }}>
        <FormControl autoComplete="off" size="small" sx={{ minWidth: 240 }}>
          <InputLabel>Roles</InputLabel>
          <Select
            multiple
            value={setting.value}
            onChange={(e) =>
              setRowEdits((prev) => ({
                ...prev,
                [row.id]: { ...setting, value: e.target.value },
              }))
            }
            input={<OutlinedInput label="Roles" />}
            renderValue={(selected) =>
              selected
                .map((r) => roleLabelMap[r] || r)
                .join(", ")
            }
            sx={{
              maxWidth: 340,
              ".MuiSelect-select": {
                display: "flex",
                flexWrap: "wrap",
                gap: 0.5,
                padding: "4px 8px",
              },
            }}
          >
            {availableRoles.map((r) => (
              <MenuItem key={r} value={r}>
                {roleLabelMap[r]}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button
          size="small"
          variant="contained"
          onClick={() => handleApplyEdit(row.id)}
          disabled={!setting.value.length}
        >
          Apply
        </Button>
        <Button
          size="small"
          variant="outlined"
          color="secondary"
          onClick={() =>
            setRowEdits((prev) => {
              const c = { ...prev };
              delete c[row.id];
              return c;
            })
          }
        >
          Cancel
        </Button>
      </Box>
    );
  };

  // Render top toolbar
  const renderTopToolbar = () => (
    <Box className="flex items-center w-full px-4 py-2 gap-2">
      <Button variant="contained" onClick={() => setOpenCreate(true)}>
        + Add User
      </Button>
      <FormControl autoComplete="off"
        size="small"
        sx={{
          minWidth: 180,
          height: 40,
          justifyContent: "center",
          '& .MuiInputBase-root': {
            height: 40,
            alignItems: "center",
            display: "flex",
          },
          '& .MuiSelect-select': {
            paddingTop: "8px",
            paddingBottom: "8px",
            minHeight: "unset",
          },
        }}
      >
        <InputLabel>Bulk Roles</InputLabel>
        <Select
          multiple
          value={bulkRoles}
          label="Bulk Roles"
          onChange={e => setBulkRoles(e.target.value)}
          renderValue={selected => selected.map(r => roleLabelMap[r]).join(', ')}
          displayEmpty
        >
          {availableRoles.map(r => (
            <MenuItem key={r} value={r}>
              {roleLabelMap[r]}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <Button
        variant="outlined"
        onClick={handleBulkUpdateRoles}
        disabled={!bulkRoles.length || !Object.keys(rowSelection).length}
      >
        Bulk Update Roles
      </Button>
      <Button
        variant="outlined"
        color="secondary"
        onClick={() => setRowSelection({})}
      >
        Clear Selections
      </Button>
    </Box>
  );

  return (
    <Box>
      <CreateNewUserModal
        open={openCreate}
        onClose={() => setOpenCreate(false)}
        onCreated={() => {
          setOpenCreate(false);
          onCreated();
        }}
        roles={availableRoles}
      />

      {/* Reset Password Dialog */}
      <Dialog open={resetDialog.open} onClose={() => setResetDialog({ open: false, userId: null, currentPassword: "", newPassword: "", confirmNewPassword: "" })} maxWidth="xs" fullWidth>
        <DialogTitle>Reset Password</DialogTitle>
        <DialogContent dividers>
          <TextField
            label="Current Password"
            type="password"
            fullWidth
            margin="dense"
            value={resetDialog.currentPassword}
            onChange={(e) => setResetDialog((prev) => ({ ...prev, currentPassword: e.target.value }))}
            autoFocus
          />
          <TextField
            label="New Password"
            type="password"
            fullWidth
            margin="dense"
            value={resetDialog.newPassword}
            onChange={(e) => setResetDialog((prev) => ({ ...prev, newPassword: e.target.value }))}
          />
          <TextField
            label="Confirm New Password"
            type="password"
            fullWidth
            margin="dense"
            value={resetDialog.confirmNewPassword}
            onChange={(e) => setResetDialog((prev) => ({ ...prev, confirmNewPassword: e.target.value }))}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResetDialog({ open: false, userId: null, currentPassword: "", newPassword: "", confirmNewPassword: "" })}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleResetPassword}
            disabled={
              !resetDialog.currentPassword ||
              !resetDialog.newPassword ||
              !resetDialog.confirmNewPassword
            }
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, userId: null, password: "" })} maxWidth="xs" fullWidth>
        <DialogTitle>Confirm Delete User</DialogTitle>
        <DialogContent dividers>
          <TextField
            label="Enter your password to confirm"
            type="password"
            fullWidth
            margin="dense"
            value={deleteDialog.password}
            onChange={(e) => setDeleteDialog((prev) => ({ ...prev, password: e.target.value }))}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, userId: null, password: "" })}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            disabled={!deleteDialog.password}
            onClick={confirmDelete}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      <Box className="px-3 pt-2" sx={{ width: "100%", minWidth: 0 }}>
      <MaterialReactTable
        columns={columns}
        data={data}
        layoutMode="grid"
        displayColumnDefOptions={{
          "mrt-row-drag": {
            header: "Move",
            headerPosition: "left",
          },
        }}
        enablePagination
        enableRowSelection
        getRowId={(row) => row.id}
        enableSelectAll
        enableColumnOrdering
        enableColumnResizing
        columnResizeMode="onChange"
        onRowSelectionChange={setRowSelection}
        state={{
          rowSelection,
          columnOrder,
          columnSizing,
          pagination: { pageIndex: page, pageSize },
          editingRowIds: Object.keys(rowEdits),
        }}
        manualPagination={false}
        onPaginationChange={handlePaginationChange}
        onColumnOrderChange={handleColumnOrderChange}
        onColumnSizingChange={handleColumnSizingChange}
        renderTopToolbarCustomActions={renderTopToolbar}
        muiTableBodyRowProps={({ row }) => ({
          sx: rowEdits[row.original.id]
            ? { backgroundColor: "#181f36" }
            : {},
        })}
        renderRowActions={({ row }) =>
          rowEdits[row.original.id]
            ? renderEditRow(row.original)
            : undefined
        }
        {...tableStyling}
      />
      </Box>
    </Box>
  );
}
