import API from "../../../services/api";

export function useSupplierActions({
  setPreview,
  setImportFile,
  setShowDuplicateDialog,
  fetchSuppliers,
  setRowSelection,
  setOpenCreate,
  setEditingSupplier,
  toast,
}) {
  // Import file handler
  const handleImport = async (event) => {
    const file = event?.target?.files?.[0] || event;
    if (!file) return;
    const allowed = [
      "text/csv",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel",
    ];
    if (
      (file.type && !allowed.includes(file.type)) &&
      ![".csv", ".xlsx"].some((ext) =>
        file.name.toLowerCase().endsWith(ext)
      )
    ) {
      toast.error("Please select a CSV or XLSX file.");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("file", file);
      const res = await API.post("/suppliers/import/preview", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      setPreview(res.data.preview || []);
      setImportFile(file);
      const hasDupes = (res.data.preview || []).some((r) => r.duplicates);
      if (hasDupes) {
        setShowDuplicateDialog(true);
      } else {
        await handleCommitImport(
          (res.data.preview || []).map((r) => ({ ...r, action: "import" }))
        );
      }
    } catch (err) {
      console.error("Import error:", err);
      toast.error(
        "Import failed: " +
        (err.response?.data?.detail || err.message || "Unknown error")
      );
    }
  };

  // Commit import
  const handleCommitImport = async (actions) => {
    try {
      await API.post("/suppliers/import/commit", actions, {
        headers: { "Content-Type": "application/json" },
      });
      toast.success("Suppliers imported successfully!");
      setShowDuplicateDialog(false);
      setPreview(null);
      setImportFile(null);
      fetchSuppliers();
    } catch (err) {
      console.error("Import commit error:", err);
      toast.error(
        "Import commit failed: " + (err.response?.data?.detail || err.message)
      );
    }
  };

  // Delete selected
  const handleDelete = async (rowSelection) => {
    const ids = Object.keys(rowSelection);
    if (!ids.length) {
      return toast.error("No suppliers selected");
    }
    if (!window.confirm(`Delete ${ids.length} supplier(s)?`)) {
      return;
    }
    try {
      await Promise.all(
        ids.map((id) => API.delete(`/suppliers/${id}`))
      );
      toast.success("Deleted selected suppliers");
      setRowSelection({});
      fetchSuppliers();
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete selected suppliers: " + (error.response?.data?.detail || error.message));
    }
  };

  // Mass update
  const handleMassUpdate = async (rowSelection, updateData) => {
    const ids = Object.keys(rowSelection);
    if (!ids.length) {
      return toast.error("No suppliers selected.");
    }
    try {
      const updates = ids.map((id) => ({
        id,
        ...updateData,
      }));
      await API.post("/suppliers/mass_update", updates);
      toast.success("Mass update applied!");
      setRowSelection({});
      fetchSuppliers();
    } catch (error) {
      console.error("Mass update error:", error);
      toast.error("Update failed: " + (error.response?.data?.detail || error.message));
    }
  };

  return {
    handleImport,
    handleCommitImport,
    handleDelete: () => handleDelete,
    handleMassUpdate: () => handleMassUpdate,
  };
}
