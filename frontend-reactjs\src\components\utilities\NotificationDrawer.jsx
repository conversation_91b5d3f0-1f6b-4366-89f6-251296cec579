import React, { useEffect, useRef } from "react";
import clsx from "clsx";

export default function NotificationDrawer({ open, onClose, notifications }) {
  const drawerRef = useRef();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (drawerRef.current && !drawerRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [open, onClose]);

  return (
    <>
      <div
        onClick={onClose}
        className={clsx(
          "fixed inset-0 bg-black bg-opacity-30 transition-opacity",
          open ? "opacity-100 pointer-events-auto z-[9999]" : "opacity-0 pointer-events-none"
        )}
      />

      <div
        ref={drawerRef}
        className={clsx(
          "fixed top-0 right-0 h-full w-1/4 bg-gray-900 text-white shadow-lg border-l border-purple-600 transform transition-transform duration-300 ease-in-out",
          open
            ? "translate-x-0 z-[10000]"
            : "translate-x-full z-[10000]"
        )}
      >
        <div className="flex justify-between items-center px-4 py-3 border-b border-gray-700 bg-gray-800 sticky top-0 z-10">
          <h2 className="text-lg font-semibold">Notifications</h2>
          <button
            onClick={onClose}
            className="text-sm bg-purple-600 hover:bg-purple-500 px-3 py-1 rounded"
          >
            Close
          </button>
        </div>

        <div className="p-4 overflow-y-auto h-full space-y-4">
          {notifications.length === 0 ? (
            <p className="text-gray-400">No notifications yet.</p>
          ) : (
            notifications
              .slice()
              .reverse()
              .map((n, idx) => (
                <div
                  key={idx}
                  className={clsx(
                    "border-l-4 p-3 bg-gray-800",
                    n.type === "error" ? "border-red-500" : "border-green-500"
                  )}
                >
                  <div className="text-sm">{n.message}</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(n.timestamp).toLocaleString()}
                  </div>
                </div>
              ))
          )}
        </div>
      </div>
    </>
  );
}

