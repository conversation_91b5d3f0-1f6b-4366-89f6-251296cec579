import { useState, useEffect } from 'react';

export function usePagination(
  tableKey,
  { initialPage = 0, initialSize = 20 } = {}
) {
  const [pageIndex, setPageIndex] = useState(initialPage);
  const [pageSize,  setPageSize]  = useState(initialSize);

  // load saved pageIndex/pageSize
  useEffect(() => {
    const saved = JSON.parse(localStorage.getItem(`${tableKey}-pagination`) ?? '{}');
    if (typeof saved.pageIndex === 'number') setPageIndex(saved.pageIndex);
    if (typeof saved.pageSize  === 'number') setPageSize(saved.pageSize);
  }, [tableKey]);

  // persist changes
  useEffect(() => {
    localStorage.setItem(
      `${tableKey}-pagination`,
      JSON.stringify({ pageIndex, pageSize })
    );
  }, [tableKey, pageIndex, pageSize]);

  return { pageIndex, pageSize, setPageIndex, setPageSize };
}
