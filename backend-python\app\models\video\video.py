from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime


class Video(SQLModel, table=True):
    __tablename__ = "video"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    title: str = Field(max_length=255)
    description: Optional[str] = Field(default="", max_length=1000)
    filename: str = Field(max_length=255)
    original_filename: str = Field(max_length=255)
    file_url: str = Field(max_length=500)
    file_size: int = Field(default=0)  # Size in bytes
    duration: Optional[float] = Field(default=None)  # Duration in seconds
    width: Optional[int] = Field(default=None)  # Video width in pixels
    height: Optional[int] = Field(default=None)  # Video height in pixels
    format: Optional[str] = Field(default="", max_length=50)  # Video format (mp4, avi, etc.)
    codec: Optional[str] = Field(default="", max_length=50)  # Video codec
    bitrate: Optional[int] = Field(default=None)  # Bitrate in kbps
    frame_rate: Optional[float] = Field(default=None)  # Frames per second
    thumbnail_url: Optional[str] = Field(default="", max_length=500)  # Thumbnail image URL
    uploaded_by: Optional[int] = Field(default=None, foreign_key="user.id")
    uploaded_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    is_public: bool = Field(default=False)  # Whether video is publicly accessible
    is_processed: bool = Field(default=False)  # Whether video processing is complete
    tags: Optional[str] = Field(default="", max_length=500)  # Comma-separated tags
    category: Optional[str] = Field(default="", max_length=100)  # Video category
    
    # Relationships
    # uploaded_by_user: Optional["User"] = Relationship(back_populates="videos")


class VideoMetadata(SQLModel, table=True):
    __tablename__ = "video_metadata"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    video_id: int = Field(foreign_key="video.id")
    metadata_key: str = Field(max_length=100)
    metadata_value: str = Field(max_length=1000)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    video: Optional[Video] = Relationship()


class VideoView(SQLModel, table=True):
    __tablename__ = "video_view"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    video_id: int = Field(foreign_key="video.id")
    viewer_ip: Optional[str] = Field(default="", max_length=45)  # Support IPv6
    user_id: Optional[int] = Field(default=None, foreign_key="user.id")
    viewed_at: datetime = Field(default_factory=datetime.utcnow)
    watch_duration: Optional[float] = Field(default=None)  # How long they watched in seconds
    
    # Relationships
    video: Optional[Video] = Relationship()
    # viewer: Optional["User"] = Relationship()
