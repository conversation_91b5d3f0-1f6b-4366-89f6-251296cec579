from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import date, datetime

class Supplier(SQLModel, table=True):
    __tablename__ = "supplier"
    id: Optional[int] = Field(default=None, primary_key=True)
    company_name: str = Field(nullable=False, index=True)
    company_code: str = Field(nullable=False, index=True)
    notes: str = Field(default="")

    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_by: Optional[str] = None

    addresses: List["SupplierAddress"] = Relationship(back_populates="supplier")
    contacts: List["SupplierContact"] = Relationship(back_populates="supplier")
    documents: List["SupplierDocument"] = Relationship(back_populates="supplier")

    profile: Optional["SupplierProfile"] = Relationship(back_populates="supplier")

class SupplierAddress(SQLModel, table=True):
    __tablename__ = "supplier_address"
    id: Optional[int] = Field(default=None, primary_key=True)
    supplier_id: int = Field(foreign_key="supplier.id")
    type: str
    is_primary: bool = Field(default=False)
    street_address: str
    city: str
    state: str
    zipcode: str
    phone: Optional[str] = None
    contact_email: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

    supplier: Optional[Supplier] = Relationship(back_populates="addresses")

class SupplierContact(SQLModel, table=True):
    __tablename__ = "supplier_contact"
    id: Optional[int] = Field(default=None, primary_key=True)
    supplier_id: int = Field(foreign_key="supplier.id")
    name: str
    title: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_primary: bool = Field(default=False)

    supplier: Optional[Supplier] = Relationship(back_populates="contacts")

class SupplierDocument(SQLModel, table=True):
    __tablename__ = "supplier_document"
    id: Optional[int] = Field(default=None, primary_key=True)
    supplier_id: int = Field(foreign_key="supplier.id")
    filename: str
    file_url: str
    uploaded_at: datetime = Field(default_factory=datetime.utcnow)
    type: str = Field(default="other")

    supplier: Optional[Supplier] = Relationship(back_populates="documents")

class SupplierProfile(SQLModel, table=True):
    __tablename__ = "supplier_profile"
    id: Optional[int] = Field(default=None, primary_key=True)
    supplier_id: int = Field(foreign_key="supplier.id", unique=True)
    payment_terms: Optional[str] = None
    credit_limit: Optional[float] = None
    credit_hold: bool = Field(default=False)
    sla_description: Optional[str] = None
    contract_url: Optional[str] = None
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None

    supplier: Optional["Supplier"] = Relationship(back_populates="profile")