from sqlmodel import SQLModel, Field, Relationship, Column, DateTime, String, Integer, ForeignKey
from sqlalchemy import Index
from datetime import datetime
from typing import List, Optional

class UserRole(SQLModel, table=True):
    __tablename__ = "user_roles"
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    role: str = Field(sa_column=Column(String(32), nullable=False))

    # Relationship backref is not required, but you can add if you want
    user: Optional["User"] = Relationship(back_populates="roles")

class User(SQLModel, table=True):
    __tablename__ = "users"
    __table_args__ = (Index("ix_users_username", "username"),)  # Adds index for username

    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(sa_column=Column(String(64), unique=True, nullable=False))
    email: str = Field(index=True, nullable=False)
    password_hash: str = Field(sa_column=Column(String(255), nullable=False))
    last_login: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, nullable=True))

    # Relationships
    roles: List["UserRole"] = Relationship(back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"})

    # If you want to add created_at or updated_at fields:
    # created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    # updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)

# If you want to automatically create a join table for many-to-many,
# the above pattern with UserRole is the preferred approach for explicit role management.
