from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, Path, Body
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse
from sqlmodel import Session, select
from sqlalchemy import func, insert, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from datetime import datetime
from typing import Optional
import pandas as pd
import numpy as np
import math
import logging
import io
import os, shutil
from fuzzywuzzy import process
from uuid import uuid4
from datetime import datetime

from database.database import get_db
from models.supplier.supplier import (
    Supplier,
    SupplierAddress,
    SupplierContact,
    SupplierDocument,
    SupplierProfile,
)
from schemas.suppliers.supplier import (
    SupplierCreate,
    SupplierRead,
    SupplierUpdate,
    SupplierAddressCreate,
    SupplierContactBase,
    SupplierProfileCreate,
)
from schemas.suppliers.supplier_documents import SupplierDocumentCreate
from schemas.suppliers.supplier_mass_update import SupplierMassUpdate
from models.supplier.supplier import SupplierDocument
from routers.auth.auth import get_current_user
from routers.permissions.permissions import require_permission
from utilities.validation.field_validator import clean_str, clean_num, clean_bool
from utilities.uploads.upload_paths import DOCUMENTS_DIR, CONTRACTS_DIR
from utilities.uploads.upload_paths import UPLOADS_BASE as BASE_UPLOAD_DIR

router = APIRouter(prefix="/suppliers", tags=["Suppliers"])

logger = logging.getLogger("supplier_import")
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

HEADER_ALIASES = {
    "company_name": ["company_name", "supplier_name"],
    "company_code": ["company_code", "code"],
    "notes": ["notes"],
    "created_at": ["created_at", "creation_date"],
    "updated_at": ["updated_at", "last_updated"],
    "updated_by": ["updated_by", "last_updated_by"],
    "payment_terms": ["payment_terms"],
    "credit_limit": ["credit_limit"],
    "credit_hold": ["credit_hold"],
    "sla_description": ["sla_description", "service_level_agreement"],
    "contract_url": ["contract_url", "contract_url"],
    "effective_date": ["effective_date", "contract_start_date"],
    "expiration_date": ["expiration_date", "contract_end_date"],
    # Billing Address
    "billing_street_address": ["billing_street_address"],
    "billing_city": ["billing_city"],
    "billing_state": ["billing_state"],
    "billing_zipcode": ["billing_zipcode"],
    "billing_phone": ["billing_phone"],
    "billing_contact_email": ["billing_contact_email"],
    "billing_notes": ["billing_notes"],
    # Shipping Address
    "shipping_street_address": ["shipping_street_address"],
    "shipping_city": ["shipping_city"],
    "shipping_state": ["shipping_state"],
    "shipping_zipcode": ["shipping_zipcode"],
    "shipping_phone": ["shipping_phone"],
    "shipping_contact_email": ["shipping_contact_email"],
    "shipping_notes": ["shipping_notes"],
    # Primary Contact
    "primary_contact_name": ["primary_contact_name"],
    "primary_contact_email": ["primary_contact_email"],
    "primary_contact_phone": ["primary_contact_phone"],
    "primary_contact_title": ["primary_contact_title"],
    # Additional Contacts
    **{f"contact{i}_{field}": [f"contact{i}_{field}"] for i in range(2,6) for field in ("name","email","phone","is_primary")},
    # Documents
    **{f"document{i}_{suffix}": [f"document{i}_{suffix}", f"doc{i}_{'name' if suffix=='filename' else 'url'}"]
      for i in range(1,11) for suffix in ("filename","url")},
}

ALLOWED_TYPES = {
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/pdf",
    "text/csv",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "image/png",
    "image/jpeg",
}

@contextmanager
def atomic_transaction(session: Session):
    try:
        yield
        session.commit()
    except:
        session.rollback()
        raise

def fuzzy_map_headers(df_headers):
    mapped = {}
    for canon, aliases in HEADER_ALIASES.items():
        best, score = process.extractOne(canon, df_headers)
        if score >= 90:
            mapped[best] = canon
        else:
            # check aliases
            best_alias, best_score = None, 0
            for a in aliases:
                m, s = process.extractOne(a, df_headers)
                if s > best_score:
                    best_alias, best_score = m, s
            if best_score >= 85:
                mapped[best_alias] = canon
    return mapped

def get_address(supplier, t):
    d = {}
    addr = next((a for a in supplier.addresses if a.type==t), None)
    if addr:
        d.update({
            f"{t}_street_address": addr.street_address,
            f"{t}_city": addr.city,
            f"{t}_state": addr.state,
            f"{t}_zipcode": addr.zipcode,
            f"{t}_phone": addr.phone,
            f"{t}_contact_email": addr.contact_email,
            f"{t}_notes": addr.notes,
        })
    return d

def set_primary_on_address(addresses):
    """Set first billing and shipping address in addresses as primary, if none are marked as primary."""
    billing = [a for a in addresses if getattr(a, "type", None) == "billing"]
    shipping = [a for a in addresses if getattr(a, "type", None) == "shipping"]
    if billing and not any(getattr(a, "is_primary", False) for a in billing):
        billing[0].is_primary = True
    if shipping and not any(getattr(a, "is_primary", False) for a in shipping):
        shipping[0].is_primary = True

def set_primary_on_contact(contacts):
    """Ensure only one primary contact exists. Make the first one primary if none set."""
    found_primary = False
    for c in contacts:
        if getattr(c, "is_primary", False):
            if not found_primary:
                found_primary = True
            else:
                c.is_primary = False 
    if not found_primary and contacts:
        contacts[0].is_primary = True

def get_primary_contact(supplier):
    d = {}
    pc = next((c for c in supplier.contacts if c.is_primary), None)
    if pc:
        d.update({
            "primary_contact_name": pc.name,
            "primary_contact_email": pc.email,
            "primary_contact_phone": pc.phone,
            "primary_contact_title": pc.title,
        })
    return d

@router.post("/import/preview")
async def import_preview(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        if file.filename.endswith(".csv"):
            df = pd.read_csv(io.StringIO(contents.decode("utf-8")), dtype=str)
        elif file.filename.endswith((".xls", ".xlsx")):
            df = pd.read_excel(io.BytesIO(contents))
            df = df.astype(str)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type.")

        if df is None or df.empty or len(df.columns) == 0:
            raise HTTPException(status_code=400, detail="Uploaded file is empty or invalid.")

        df = df.replace([np.inf, -np.inf], None)
        df = df.where(pd.notnull(df), None)
        df.columns = [col.strip() for col in df.columns]

        # Map headers
        mapped_headers = fuzzy_map_headers(df.columns)
        df = df.rename(columns={original: mapped_headers[original] for original in mapped_headers})

        preview_data = []

        for index, row in df.iterrows():
            imported_data = {}

            # Apply cleaning for each canonical header
            for canon_key, aliases in HEADER_ALIASES.items():
                value = None
                for alias in [canon_key] + list(aliases):
                    if alias in row:
                        value = row.get(alias)
                        break

                # Cleaning per field type
                if canon_key in ["credit_limit"]:
                    imported_data[canon_key] = clean_num(value)
                elif canon_key in ["credit_hold"]:
                    imported_data[canon_key] = clean_bool(value)
                else:
                    imported_data[canon_key] = clean_str(value)

            # Handle special mapping for contacts and emails (leave as in your code)
            if not imported_data.get("billing_contact_email"):
                for i in range(2, 11):
                    val = imported_data.get(f"contact{i}_email")
                    if val:
                        imported_data["billing_contact_email"] = val
                        break
                if not imported_data.get("billing_contact_email") and imported_data.get("primary_contact_email"):
                    imported_data["billing_contact_email"] = imported_data["primary_contact_email"]

            for contact_field in ["name", "email", "phone", "title"]:
                if not imported_data.get(f"primary_contact_{contact_field}"):
                    for i in range(2, 11):
                        val = imported_data.get(f"contact{i}_{contact_field}")
                        if val:
                            imported_data[f"primary_contact_{contact_field}"] = val
                            break

            company_name = imported_data.get("company_name", "")
            company_code = imported_data.get("company_code", "")
            existing_supplier = None

            if company_name and company_code:
                existing_supplier = db.exec(
                    select(Supplier)
                    .options(
                        selectinload(Supplier.addresses),
                        selectinload(Supplier.contacts),
                        selectinload(Supplier.documents),
                        selectinload(Supplier.profile),
                    )
                    .where(
                        (func.lower(Supplier.company_name) == company_name.lower()) &
                        (func.lower(Supplier.company_code) == company_code.lower())
                    )
                ).first()

            preview_row = {
                "id": index,
                "imported": imported_data,
                "existing": None,
                "action": "import",
                "duplicates": False,
                "merge_fields": {}
            }

            if existing_supplier:
                preview_row["duplicates"] = True
                preview_row["existing"] = SupplierRead.from_orm(existing_supplier).dict()

            preview_data.append(preview_row)

        return {"preview": preview_data}

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error during import preview: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to process import preview: {e}")

@router.post(
    "/import/commit",
    dependencies=[Depends(require_permission("Suppliers", "update"))],
)
async def commit_import_suppliers(
    actions: List[Dict[str, Any]] = Body(...),
    db: Session = Depends(get_db),
):
    imported, updated, skipped = 0, 0, 0
    logger.info(f"Received {len(actions)} actions for import commit.")

    try:
        with atomic_transaction(db):
            for i, item in enumerate(actions):
                imported_row = item.get("imported", {})
                action = item.get("action")

                # Clean main fields from "imported"
                company_name_import = clean_str(imported_row.get("company_name"))
                company_code_import = clean_str(imported_row.get("company_code"))
                notes_import = clean_str(imported_row.get("notes"))

                logger.info(
                    f"Processing row {i+1}: "
                    f"company_name='{company_name_import}', "
                    f"company_code='{company_code_import}', action='{action}'"
                )

                # Fetch existing supplier (if both name & code are present)
                existing = None
                if company_name_import and company_code_import:
                    existing = db.exec(
                        select(Supplier)
                        .options(
                            selectinload(Supplier.addresses),
                            selectinload(Supplier.contacts),
                            selectinload(Supplier.documents),
                            selectinload(Supplier.profile),
                        )
                        .where(
                            (Supplier.company_name.ilike(company_name_import))
                            & (Supplier.company_code.ilike(company_code_import))
                        )
                    ).first()

                if existing:
                    logger.info(
                        f"Found existing supplier: "
                        f"id={existing.id}, name='{existing.company_name}', "
                        f"code='{existing.company_code}'"
                    )
                else:
                    logger.info(
                        f"No existing supplier found for "
                        f"company_name='{company_name_import}', "
                        f"company_code='{company_code_import}'."
                    )

                # --- Action: skip ---
                if action == "skip":
                    skipped += 1
                    logger.info(f"Skipping row {i+1} as per action 'skip'.")
                    continue

                # --- Build DTOs for children from imported_row ---

                # Contacts
                contacts: List[SupplierContactBase] = []
                primary_contact_data = {
                    "name": clean_str(imported_row.get("primary_contact_name")),
                    "email": clean_str(imported_row.get("primary_contact_email")),
                    "phone": clean_str(imported_row.get("primary_contact_phone")),
                    "title": clean_str(imported_row.get("primary_contact_title")),
                    "is_primary": True,
                }
                if any(primary_contact_data.values()):
                    contacts.append(SupplierContactBase(**primary_contact_data))

                for j in range(2, 11):
                    name = clean_str(imported_row.get(f"contact{j}_name"))
                    email = clean_str(imported_row.get(f"contact{j}_email"))
                    phone = clean_str(imported_row.get(f"contact{j}_phone"))
                    title = clean_str(imported_row.get(f"contact{j}_title"))
                    is_primary = clean_bool(imported_row.get(f"contact{j}_is_primary", False))
                    if not (name or email or phone):
                        continue
                    contacts.append(
                        SupplierContactBase(
                            name=name,
                            email=email,
                            phone=phone,
                            title=title,
                            is_primary=is_primary,
                        )
                    )

                # Documents
                documents: List[SupplierDocumentCreate] = []
                for j in range(1, 11):
                    filename = clean_str(imported_row.get(f"document{j}_filename"))
                    file_url = clean_str(imported_row.get(f"document{j}_url") or "")
                    if filename:
                        documents.append(
                            SupplierDocumentCreate(
                                filename=filename,
                                file_url=file_url,
                            )
                        )

                # Addresses helper
                def build_address(prefix: str) -> Dict[str, Any]:
                    return {
                        "type": prefix,
                        "street_address": clean_str(imported_row.get(f"{prefix}_street_address")),
                        "city": clean_str(imported_row.get(f"{prefix}_city")),
                        "state": clean_str(imported_row.get(f"{prefix}_state")),
                        "zipcode": clean_str(imported_row.get(f"{prefix}_zipcode")),
                        "phone": clean_str(imported_row.get(f"{prefix}_phone")),
                        "contact_email": clean_str(imported_row.get(f"{prefix}_contact_email")),
                        "notes": clean_str(imported_row.get(f"{prefix}_notes")),
                    }

                addresses: List[SupplierAddressCreate] = []
                if any(
                    clean_str(imported_row.get(k))
                    for k in [
                        "billing_street_address",
                        "billing_city",
                        "billing_state",
                        "billing_zipcode",
                        "billing_phone",
                        "billing_contact_email",
                        "billing_notes",
                    ]
                ):
                    addresses.append(SupplierAddressCreate(**build_address("billing")))

                if any(
                    clean_str(imported_row.get(k))
                    for k in [
                        "shipping_street_address",
                        "shipping_city",
                        "shipping_state",
                        "shipping_zipcode",
                        "shipping_phone",
                        "shipping_contact_email",
                        "shipping_notes",
                    ]
                ):
                    addresses.append(SupplierAddressCreate(**build_address("shipping")))

                # Profile fields
                profile_fields = [
                    "payment_terms",
                    "credit_limit",
                    "credit_hold",
                    "sla_description",
                    "contract_url",
                    "effective_date",
                    "expiration_date",
                ]
                profile_data = {
                    "payment_terms": clean_str(imported_row.get("payment_terms")),
                    "credit_limit": clean_num(imported_row.get("credit_limit")),
                    "credit_hold": clean_bool(imported_row.get("credit_hold")),
                    "sla_description": clean_str(imported_row.get("sla_description")),
                    "contract_url": clean_str(imported_row.get("contract_url")),
                    "effective_date": clean_str(imported_row.get("effective_date")),
                    "expiration_date": clean_str(imported_row.get("expiration_date")),
                }
                profile_obj = None
                if any(v is not None and v != "" for v in profile_data.values()):
                    profile_obj = SupplierProfileCreate(**profile_data)

                # --- Handle existing supplier: overwrite or merge ---
                if existing:
                    # --- OVERWRITE ---
                    if action == "overwrite":
                        logger.info(f"Performing 'overwrite' for supplier id={existing.id}.")

                        # Delete all child rows first
                        db.exec(delete(SupplierAddress).where(SupplierAddress.supplier_id == existing.id))
                        db.exec(delete(SupplierContact).where(SupplierContact.supplier_id == existing.id))
                        db.exec(delete(SupplierDocument).where(SupplierDocument.supplier_id == existing.id))
                        db.exec(delete(SupplierProfile).where(SupplierProfile.supplier_id == existing.id))

                        # Optional: flush and refresh if needed, but not required just to set fields
                        db.flush()
                        db.refresh(existing)

                        # Update main fields
                        existing.company_name = company_name_import
                        existing.company_code = company_code_import
                        existing.notes = notes_import

                        # Re-insert child rows
                        for a in addresses:
                            db.add(SupplierAddress(**a.dict(), supplier_id=existing.id))
                        for c in contacts:
                            db.add(SupplierContact(**c.dict(), supplier_id=existing.id))
                        for d in documents:
                            db.add(SupplierDocument(**d.dict(), supplier_id=existing.id))
                        if profile_obj is not None:
                            db.add(SupplierProfile(**profile_obj.dict(), supplier_id=existing.id))

                        # Persist updates
                        db.add(existing)
                        updated += 1
                        logger.info(f"Successfully overwrote supplier id={existing.id}.")
                        continue

                    # --- MERGE ---
                    elif action == "merge":
                        logger.info(f"Performing 'merge' for supplier id={existing.id}.")
                        merge_fields = item.get("merge_fields", {})
                        logger.info(f"Merge fields for supplier id={existing.id}: {merge_fields}")

                        # MAIN FIELDS: overwrite if key exists in merge_fields
                        for field in ["company_name", "company_code", "notes"]:
                            if field in merge_fields:
                                raw = merge_fields[field]
                                setattr(existing, field, clean_str(raw))

                        # PROFILE FIELDS: ensure profile exists, then overwrite if key exists
                        if not existing.profile:
                            existing.profile = SupplierProfile(supplier_id=existing.id)

                        for field in [
                            "payment_terms",
                            "credit_limit",
                            "credit_hold",
                            "sla_description",
                            "contract_url",
                            "effective_date",
                            "expiration_date",
                        ]:
                            if field in merge_fields:
                                raw = merge_fields[field]
                                if field == "credit_limit":
                                    val = clean_num(raw)
                                elif field == "credit_hold":
                                    val = clean_bool(raw)
                                else:
                                    val = clean_str(raw)
                                setattr(existing.profile, field, val)

                        # Merge ADDRESSES: update existing by type or add new
                        for new_address in addresses:
                            found_existing_address = False
                            for existing_address in existing.addresses:
                                if existing_address.type == new_address.type:
                                    for k, v in new_address.dict().items():
                                        if v is not None:
                                            setattr(existing_address, k, v)
                                    found_existing_address = True
                                    break
                            if not found_existing_address:
                                db.add(SupplierAddress(**new_address.dict(), supplier=existing))

                        # Merge CONTACTS: update or add
                        for new_contact in contacts:
                            found_existing_contact = False
                            if new_contact.is_primary:
                                for existing_contact in existing.contacts:
                                    if existing_contact.is_primary:
                                        for k, v in new_contact.dict().items():
                                            if v is not None:
                                                setattr(existing_contact, k, v)
                                        found_existing_contact = True
                                        break
                                if not found_existing_contact:
                                    for ec in existing.contacts:
                                        ec.is_primary = False
                                    db.add(SupplierContact(**new_contact.dict(), supplier=existing))
                                    found_existing_contact = True
                            else:
                                for existing_contact in existing.contacts:
                                    if (
                                        existing_contact.name == new_contact.name
                                        and existing_contact.email == new_contact.email
                                    ):
                                        found_existing_contact = True
                                        break
                                if not found_existing_contact:
                                    db.add(SupplierContact(**new_contact.dict(), supplier=existing))

                        # Merge DOCUMENTS: add new if not duplicate
                        for new_document in documents:
                            if not any(
                                (doc.filename == new_document.filename and doc.file_url == new_document.file_url)
                                for doc in existing.documents
                            ):
                                db.add(SupplierDocument(**new_document.dict(), supplier=existing))

                        # Persist merged supplier
                        db.add(existing)
                        updated += 1
                        logger.info(f"Successfully merged supplier id={existing.id}.")
                        continue

                # --- CREATE NEW SUPPLIER if not existing ---
                logger.info(
                    f"Creating new supplier for company_name='{company_name_import}', "
                    f"company_code='{company_code_import}'."
                )
                payload = SupplierCreate(
                    company_name=company_name_import,
                    company_code=company_code_import,
                    notes=notes_import,
                    profile=profile_obj,
                    contacts=contacts,
                    addresses=addresses,
                    documents=documents,
                )
                inst = Supplier(**payload.dict(exclude={"addresses", "contacts", "documents", "profile"}))
                db.add(inst)
                db.flush()  # to obtain inst.id for foreign keys

                for address in addresses:
                    db.add(SupplierAddress(**address.dict(), supplier=inst))
                for contact in contacts:
                    db.add(SupplierContact(**contact.dict(), supplier=inst))
                for document in documents:
                    db.add(SupplierDocument(**document.dict(), supplier=inst))
                if profile_obj is not None:
                    db.add(SupplierProfile(**profile_obj.dict(), supplier=inst))

                imported += 1
                logger.info("Successfully created new supplier.")

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error during import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Database error during import: {e}")
    except Exception as e:
        db.rollback()
        logger.error(f"Import failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Import failed: {e}")

    logger.info(
        f"Import commit complete: Imported={imported}, Updated={updated}, Skipped={skipped}"
    )
    return {"imported": imported, "updated": updated, "skipped": skipped}

@router.post("/import")
async def import_suppliers(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
):
    try:
        contents = await file.read()
        if file.filename.endswith(".csv"):
            df = pd.read_csv(io.StringIO(contents.decode("utf-8")), dtype=str)
        elif file.filename.endswith((".xls", ".xlsx")):
            df = pd.read_excel(io.BytesIO(contents))
            df = df.astype(str)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type.")
        if df is None or df.empty or len(df.columns) == 0:
            raise HTTPException(status_code=400, detail="Uploaded file is empty or invalid.")

        df = df.replace([np.inf, -np.inf], None)
        df = df.where(pd.notnull(df), None)
        df.columns = [c.strip() for c in df.columns]
        mapped = fuzzy_map_headers(df.columns)
        df = df.rename(columns={orig: mapped[orig] for orig in mapped})

        imported_count = 0
        updated_count = 0

        with atomic_transaction(db):
            for _, row in df.iterrows():
                # --- Clean all main fields ---
                name = clean_str(row.get("company_name"))
                code = clean_str(row.get("company_code"))
                notes = clean_str(row.get("notes"))

                if not name or not code:
                    continue

                existing = db.exec(
                    select(Supplier).where(
                        Supplier.company_name.ilike(name),
                        Supplier.company_code.ilike(code),
                    )
                ).first()

                # --- Clean Contacts ---
                contacts = []
                # Primary contact
                if row.get("primary_contact_name") or row.get("primary_contact_email") or row.get("primary_contact_phone"):
                    contacts.append(SupplierContactBase(
                        name=clean_str(row.get("primary_contact_name")),
                        email=clean_str(row.get("primary_contact_email")),
                        phone=clean_str(row.get("primary_contact_phone")),
                        title=clean_str(row.get("primary_contact_title")),
                        is_primary=True,
                    ))
                for i in range(2, 10):
                    n = clean_str(row.get(f"contact{i}_name"))
                    e = clean_str(row.get(f"contact{i}_email"))
                    p = clean_str(row.get(f"contact{i}_phone"))
                    t = clean_str(row.get(f"contact{i}_title"))
                    is_p = clean_bool(row.get(f"contact{i}_is_primary", False))
                    if n or e or p:
                        contacts.append(SupplierContactBase(
                            name=n, email=e, phone=p, title=t, is_primary=is_p
                        ))

                # --- Clean Documents ---
                documents = []
                for i in range(1, 10):
                    fn = clean_str(row.get(f"document{i}_filename"))
                    if fn:
                        documents.append(SupplierDocumentCreate(
                            filename=fn,
                            file_url=clean_str(row.get(f"document{i}_url")),
                        ))

                # --- Clean Addresses ---
                addresses = []
                # Billing
                if any(clean_str(row.get(k)) for k in [
                        "billing_street_address", "billing_city", "billing_state",
                        "billing_zipcode", "billing_phone", "billing_contact_email", "billing_notes"]):
                    addresses.append(SupplierAddressCreate(
                        type="billing",
                        street_address=clean_str(row.get("billing_street_address")),
                        city=clean_str(row.get("billing_city")),
                        state=clean_str(row.get("billing_state")),
                        zipcode=clean_str(row.get("billing_zipcode")),
                        phone=clean_str(row.get("billing_phone")),
                        contact_email=clean_str(row.get("billing_contact_email")),
                        notes=clean_str(row.get("billing_notes")),
                    ))
                # Shipping
                if any(clean_str(row.get(k)) for k in [
                        "shipping_street_address", "shipping_city", "shipping_state",
                        "shipping_zipcode", "shipping_phone", "shipping_contact_email", "shipping_notes"]):
                    addresses.append(SupplierAddressCreate(
                        type="shipping",
                        street_address=clean_str(row.get("shipping_street_address")),
                        city=clean_str(row.get("shipping_city")),
                        state=clean_str(row.get("shipping_state")),
                        zipcode=clean_str(row.get("shipping_zipcode")),
                        phone=clean_str(row.get("shipping_phone")),
                        contact_email=clean_str(row.get("shipping_contact_email")),
                        notes=clean_str(row.get("shipping_notes")),
                    ))

                # --- Clean Profile fields ---
                prof_keys = [
                    "payment_terms", "credit_limit", "credit_hold",
                    "sla_description", "contract_url",
                    "effective_date", "expiration_date"
                ]
                prof_raw = {
                    "payment_terms": clean_str(row.get("payment_terms")),
                    "credit_limit": clean_num(row.get("credit_limit")),
                    "credit_hold": clean_bool(row.get("credit_hold")),
                    "sla_description": clean_str(row.get("sla_description")),
                    "contract_url": clean_str(row.get("contract_url")),
                    "effective_date": clean_str(row.get("effective_date")),
                    "expiration_date": clean_str(row.get("expiration_date")),
                }
                profile_obj = None
                if any(v not in (None, "", False) for v in prof_raw.values()):
                    profile_obj = SupplierProfileCreate(**prof_raw)

                if existing:
                    # --- UPDATE ---
                    existing.company_name = name
                    existing.company_code = code
                    existing.notes = notes

                    db.exec(delete(SupplierAddress).where(SupplierAddress.supplier_id == existing.id))
                    db.exec(delete(SupplierContact).where(SupplierContact.supplier_id == existing.id))
                    db.exec(delete(SupplierDocument).where(SupplierDocument.supplier_id == existing.id))
                    db.exec(delete(SupplierProfile).where(SupplierProfile.supplier_id == existing.id))

                    for a in addresses:
                        db.add(SupplierAddress(**a.dict(), supplier=existing))
                    for c in contacts:
                        db.add(SupplierContact(**c.dict(), supplier=existing))
                    for d in documents:
                        db.add(SupplierDocument(**d.dict(), supplier=existing))
                    if profile_obj:
                        db.add(SupplierProfile(supplier_id=existing.id, **profile_obj.dict()))
                    db.add(existing)
                    updated_count += 1

                else:
                    # --- CREATE ---
                    supplier = Supplier(
                        company_name=name,
                        company_code=code,
                        notes=notes,
                    )
                    db.add(supplier)
                    db.flush()  # obtain supplier.id
                    for a in addresses:
                        db.add(SupplierAddress(**a.dict(), supplier=supplier))
                    for c in contacts:
                        db.add(SupplierContact(**c.dict(), supplier=supplier))
                    for d in documents:
                        db.add(SupplierDocument(**d.dict(), supplier=supplier))
                    if profile_obj:
                        db.add(SupplierProfile(supplier_id=supplier.id, **profile_obj.dict()))
                    imported_count += 1

        return {"imported": imported_count, "updated": updated_count}

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except Exception:
        db.rollback()
        logger.error("Unexpected import error", exc_info=True)
        raise HTTPException(status_code=500, detail="Unexpected import error")

from typing import Optional

@router.get(
    "/",
    # response_model=YourCustomResponseModel,  # (optional for OpenAPI docs)
    dependencies=[Depends(require_permission("Suppliers", "view"))]
)
async def get_suppliers(
    db: Session = Depends(get_db),
    page: Optional[int] = Query(None, ge=1),
    page_size: Optional[int] = Query(None, ge=1, le=10000),
    sort_by: str = Query("company_name"),
    sort_order: str = Query("asc"),
    search: Optional[str] = Query(None),
):
    # 1. Build the base query (without relationships)
    base_query = select(Supplier)

    # 2. Apply search filters (if any)
    if search:
        search_pattern = f"%{search.lower()}%"
        base_query = base_query.where(
            (func.lower(Supplier.company_name).like(search_pattern)) |
            (func.lower(Supplier.company_code).like(search_pattern)) |
            (func.lower(Supplier.notes).like(search_pattern))
        )

    # 3. Get total count (before pagination)
    total = db.exec(
        select(func.count()).select_from(base_query.subquery())
    ).one()
    if isinstance(total, tuple):  # just in case
        total = total[0]

    # 4. Apply sorting
    if sort_order == "desc":
        base_query = base_query.order_by(getattr(Supplier, sort_by).desc())
    else:
        base_query = base_query.order_by(getattr(Supplier, sort_by))

    # 5. Apply pagination only if requested
    if page is not None and page_size is not None:
        base_query = base_query.offset((page - 1) * page_size).limit(page_size)

    # 6. Add relationship loading *after* all query modifications
    query_with_options = base_query.options(
        selectinload(Supplier.addresses),
        selectinload(Supplier.contacts),
        selectinload(Supplier.documents),
        selectinload(Supplier.profile)
    )

    # 7. Execute the query
    suppliers = db.exec(query_with_options).all()

    # 8. Return in consistent frontend format
    return {
        "data": [SupplierRead.from_orm(s) for s in suppliers],
        "totalCount": total
    }

@router.post(
    "/",
    response_model=SupplierRead,
    status_code=201,
    dependencies=[Depends(require_permission("Suppliers", "create"))],
)
async def create_supplier(
    supplier_create: SupplierCreate,
    db: Session           = Depends(get_db),
    current_user: dict    = Depends(get_current_user),
):
    # Check for existing supplier with same company_name and company_code
    existing_supplier = db.exec(
        select(Supplier)
        .where(
            (func.lower(Supplier.company_name) == supplier_create.company_name.lower()) |
            (func.lower(Supplier.company_code) == supplier_create.company_code.lower())
        )
    ).first()
    if existing_supplier:
        if existing_supplier.company_name.lower() == supplier_create.company_name.lower():
            raise HTTPException(status_code=409, detail="Supplier with this company name already exists")
        if existing_supplier.company_code.lower() == supplier_create.company_code.lower():
            raise HTTPException(status_code=409, detail="Supplier with this company code already exists")

    with atomic_transaction(db):
        supplier = Supplier(**supplier_create.dict(exclude={"addresses", "contacts", "documents", "profile"}))
        db.add(supplier)
        db.flush()  # Flush to get supplier.id for related objects

        set_primary_on_address(supplier_create.addresses)

        for address_data in supplier_create.addresses:
            db.add(SupplierAddress(**address_data.dict(), supplier=supplier))
        for contact_data in supplier_create.contacts:
            db.add(SupplierContact(**contact_data.dict(), supplier=supplier))
        for document_data in supplier_create.documents:
            db.add(SupplierDocument(**document_data.dict(), supplier=supplier))
        if supplier_create.profile is not None:
            db.add(SupplierProfile(**supplier_create.profile.dict(), supplier=supplier))
        else:
            # Always create a blank profile if none was provided
            db.add(SupplierProfile(supplier=supplier))

    # Reload supplier with relationships for the response
    db.refresh(supplier)
    return db.exec(
        select(Supplier)
        .options(
            selectinload(Supplier.addresses),
            selectinload(Supplier.contacts),
            selectinload(Supplier.documents),
            selectinload(Supplier.profile),
        )
        .where(Supplier.id == supplier.id)
    ).first()

@router.put(
    "/{supplier_id}",
    response_model=SupplierRead,
    dependencies=[Depends(require_permission("Suppliers", "update"))],
)
async def update_supplier(
    supplier_id: int = Path(...),
    supplier_update: SupplierUpdate = Body(...),
    db: Session = Depends(get_db),
):
    supplier = db.exec(
        select(Supplier)
        .options(
            selectinload(Supplier.addresses),
            selectinload(Supplier.contacts),
            selectinload(Supplier.documents),
            selectinload(Supplier.profile),
        )
        .where(Supplier.id == supplier_id)
    ).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")

    # Check for duplicate company_name or company_code with other suppliers
    existing_duplicate = db.exec(
        select(Supplier)
        .where(
            (Supplier.id != supplier_id) &
            (
                (func.lower(Supplier.company_name) == supplier_update.company_name.lower()) |
                (func.lower(Supplier.company_code) == supplier_update.company_code.lower())
            )
        )
    ).first()
    if existing_duplicate:
        if existing_duplicate.company_name.lower() == supplier_update.company_name.lower():
            raise HTTPException(status_code=409, detail="Supplier with this company name already exists")
        if existing_duplicate.company_code.lower() == supplier_update.company_code.lower():
            raise HTTPException(status_code=409, detail="Supplier with this company code already exists")

    with atomic_transaction(db):
        supplier.company_name = supplier_update.company_name
        supplier.company_code = supplier_update.company_code
        supplier.notes = supplier_update.notes

        db.exec(delete(SupplierAddress).where(SupplierAddress.supplier_id == supplier_id))
        db.exec(delete(SupplierContact).where(SupplierContact.supplier_id == supplier_id))
        db.exec(delete(SupplierDocument).where(SupplierDocument.supplier_id == supplier_id))

        supplier.addresses = [SupplierAddress(**a.model_dump(), supplier=supplier) for a in supplier_update.addresses]
        supplier.contacts = [SupplierContact(**c.model_dump(), supplier=supplier) for c in supplier_update.contacts]
        supplier.documents = [SupplierDocument(**d.model_dump(), supplier=supplier) for d in supplier_update.documents]
        
        if supplier_update.profile is not None:
            if supplier.profile:
                # Update existing profile
                for field, value in supplier_update.profile.model_dump().items():
                    setattr(supplier.profile, field, value)
            else:
                # No profile exists, create new
                new_profile = SupplierProfile(**supplier_update.profile.model_dump(), supplier=supplier)
                supplier.profile = new_profile
                db.add(new_profile)
        else:
            # Optionally handle the case where profile is being cleared (usually rare)
            pass

    db.refresh(supplier) 

    # Fetch again with relationships to return (this fetch is redundant if refresh is sufficient)
    supplier = db.exec(
        select(Supplier)
        .options(
            selectinload(Supplier.addresses),
            selectinload(Supplier.contacts),
            selectinload(Supplier.documents),
            selectinload(Supplier.profile),
        )
        .where(Supplier.id == supplier_id)
    ).first()
    return supplier

@router.post(
    "/mass_update",
    status_code=200,
    dependencies=[Depends(require_permission("Suppliers", "update"))],
)
async def mass_update_suppliers(
    updates: list[dict] = Body(...),
    db: Session = Depends(get_db),
):
    updated_ids = []
    for update in updates:
        supplier = db.query(Supplier).filter(Supplier.id == update["id"]).first()
        if not supplier:
            continue

        for field, value in update.items():
            if field == "id":
                continue
            # Handle nested fields (like 'profile.credit_limit')
            if "." in field:
                parent, child = field.split(".", 1)
                obj = getattr(supplier, parent, None)
                if obj and hasattr(obj, child):
                    setattr(obj, child, value)
            else:
                setattr(supplier, field, value)
        updated_ids.append(supplier.id)
    db.commit()
    return {"updated": updated_ids}

@router.post("/{supplier_id}/documents")
async def upload_document(
    supplier_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    if file.content_type not in ALLOWED_TYPES:
        raise HTTPException(status_code=400, detail="Unsupported file type.")

    # Verify supplier exists
    supplier = db.get(Supplier, supplier_id)
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found.")

    today = datetime.utcnow().strftime("%Y-%m-%d")
    uuid_str = str(uuid4())
    original_name, ext = os.path.splitext(file.filename)
    filename = f"{original_name}-{uuid_str}{ext}"

    # Save to: app/uploads/documents/{supplier_id}/{YYYY-MM-DD}/
    save_dir = os.path.join(BASE_UPLOAD_DIR, "documents", str(supplier_id), today)
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, filename)

    # Save file
    with open(save_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    # Get relative path (removes "app/" prefix for URL serving)
    relative_path = save_path.replace("app/", "").replace("\\", "/")

    # DB entry
    document = SupplierDocument(
        supplier_id=supplier_id,
        filename=file.filename,
        file_url=f"/{relative_path}",
        uploaded_at=datetime.utcnow(),
        type="document",
    )
    db.add(document)
    db.commit()
    db.refresh(document)

    return {"id": document.id, "file_url": f"/{relative_path}"}


@router.post("/{supplier_id}/contracts")
async def upload_contract(
    supplier_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    if file.content_type not in ALLOWED_TYPES:
        raise HTTPException(status_code=400, detail="Unsupported file type.")

    # Fetch supplier
    supplier = db.get(Supplier, supplier_id)
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found.")

    # Generate filename with UUID and date folder
    today = datetime.utcnow().strftime("%Y-%m-%d")
    uuid_str = str(uuid4())
    name, ext = os.path.splitext(file.filename)
    filename = f"{name}-{uuid_str}{ext}"

    # Directory: app/uploads/contracts/{supplier_id}/{YYYY-MM-DD}
    save_dir = os.path.join(BASE_UPLOAD_DIR, "contracts", str(supplier_id), today)
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, filename)

    # Save file
    with open(save_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    # Normalize for URL use
    relative_path = save_path.replace("app/", "").replace("\\", "/")

    # Save in DB
    contract = SupplierDocument(
        supplier_id=supplier_id,
        filename=file.filename,
        file_url=f"/{relative_path}",
        uploaded_at=datetime.utcnow(),
        type="contract",
    )
    db.add(contract)
    db.commit()
    db.refresh(contract)

    return {"id": contract.id, "file_url": f"/{relative_path}"}


@router.delete("/{supplier_id}/documents/{document_id}", status_code=204)
async def delete_document(
    supplier_id: int,
    document_id: int,
    db: Session = Depends(get_db)
):
    document = db.exec(
        select(SupplierDocument).where(
            SupplierDocument.id == document_id,
            SupplierDocument.supplier_id == supplier_id,
        )
    ).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Remove the file from disk
    if document.file_url and os.path.exists(document.file_url.lstrip('/')):
        os.remove(document.file_url.lstrip('/'))

    db.delete(document)
    db.commit()
    return

@router.delete("/{supplier_id}/contracts/{contract_id}", status_code=204)
async def delete_contract(
    supplier_id: int,
    contract_id: int,
    db: Session = Depends(get_db)
):
    contract = db.exec(
        select(SupplierDocument).where(
            SupplierDocument.id == contract_id,
            SupplierDocument.supplier_id == supplier_id,
        )
    ).first()
    if not contract:
        raise HTTPException(status_code=404, detail="Contract not found")

    # Remove the file from disk
    if contract.file_url and os.path.exists(contract.file_url.lstrip('/')):
        os.remove(contract.file_url.lstrip('/'))

    db.delete(contract)
    db.commit()
    return

@router.delete(
    "/{supplier_id}",
    status_code=204,
    dependencies=[Depends(require_permission("Suppliers", "delete"))],
)
async def delete_supplier(
    supplier_id: int = Path(...),
    db: Session = Depends(get_db),
):
    supplier = db.exec(select(Supplier).where(Supplier.id == supplier_id)).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")
    with atomic_transaction(db):
        # delete children first...
        db.exec(delete(SupplierAddress).where(SupplierAddress.supplier_id == supplier_id))
        db.exec(delete(SupplierContact).where(SupplierContact.supplier_id == supplier_id))
        db.exec(delete(SupplierDocument).where(SupplierDocument.supplier_id == supplier_id))
        db.exec(delete(SupplierProfile).where(SupplierProfile.supplier_id == supplier_id))
        # then the supplier itself
        db.delete(supplier)
    return  # 204 No Content

@router.get("/export", response_class=StreamingResponse)
async def export_suppliers(db: Session = Depends(get_db)):
    suppliers = db.exec(
        select(Supplier)
        .options(
            selectinload(Supplier.addresses),
            selectinload(Supplier.contacts),
            selectinload(Supplier.documents),
            selectinload(Supplier.profile),
        )
    ).all()

    rows = []
    for s in suppliers:
        # Flatten supplier data for CSV export
        billing = get_address(s, "billing")
        shipping = get_address(s, "shipping")
        primary_contact = get_primary_contact(s)
        profile = s.profile

        rows.append({
            "company_name": s.company_name,
            "company_code": s.company_code,
            "notes": s.notes,
            "created_at": s.created_at.strftime('%Y-%m-%d') if s.created_at else "",
            "updated_at": s.updated_at.strftime('%Y-%m-%d') if s.updated_at else "",
            "updated_by": s.updated_by or "",
            "payment_terms": profile.payment_terms if profile else "",
            "credit_limit": profile.credit_limit if profile else "",
            "credit_hold": profile.credit_hold if profile else "",
            "sla_description": profile.sla_description if profile else "",
            "contract_url": profile.contract_url if profile else "",
            "effective_date": profile.effective_date.strftime('%Y-%m-%d') if profile and profile.effective_date else "",
            "expiration_date": profile.expiration_date.strftime('%Y-%m-%d') if profile and profile.expiration_date else "",
            **billing,
            **shipping,
            **primary_contact,
        })

    buf = io.StringIO()
    pd.DataFrame(rows).to_csv(buf, index=False)
    buf.seek(0)
    
    return StreamingResponse(
        iter([buf.getvalue()]),
        media_type="text/csv",
        headers={"Content-Disposition": "attachment; filename=suppliers.csv"}
    )