// theme.ts
import { createTheme } from '@mui/material/styles';

export const darkTheme = createTheme({
  palette: {
    mode: 'dark',               // use dark mode so backgrounds are dark by default
    text: { primary: '#fff' },  // ensure general text is white
  },
  components: {
    MuiTablePagination: {
      styleOverrides: {
        root: {
          color: '#fff',
        },
        selectLabel: {
          color: '#fff',
        },
        displayedRows: {
          color: '#fff',
        },
        actions: {
          color: '#fff',
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        select: {
          color: '#fff',
        },
        icon: {
          color: '#fff',
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: '#fff',
        },
      },
    },
  },
});
