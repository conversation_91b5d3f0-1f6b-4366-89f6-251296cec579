from fastapi import APIRouter, Depends
from sqlmodel import select, Session
from typing import List

from database.database import get_db
from models.users.users import UserRole
from pydantic import BaseModel

router = APIRouter(prefix="/roles", tags=["Roles"])

class RoleRead(BaseModel):
    name: str

@router.get("", response_model=List[RoleRead])
def list_roles(db: Session = Depends(get_db)):
    """Return all distinct roles in the system."""
    roles = db.exec(select(UserRole.role).distinct()).all()
    return [{"name": r[0] if isinstance(r, tuple) else r} for r in roles]
