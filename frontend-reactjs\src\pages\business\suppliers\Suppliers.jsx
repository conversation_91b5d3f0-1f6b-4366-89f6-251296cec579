import React, { useState } from "react";
import SupplierTable from '../../../components/business/suppliers/SupplierTable';
import {
  Box,
  Button,
  TextField,
  Typography,
} from "@mui/material";
import { tableStyling } from '../../../styles/tableStyles.js';

export default function Suppliers() {
  const [filters, setFilters] = useState({
    company_name: "",
    company_code: "",
    billing_address: "",
    shipping_address: "",
    contact_email: "",
    phone: "",
  });

  const handleChange = (key) => (e) =>
    setFilters((prev) => ({ ...prev, [key]: e.target.value }));

  return (
      <div className="min-h-screen flex bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white p-6">
        {/* Filters Sidebar */}
        <Box sx={{ flex: 1, maxWidth: 300, marginRight: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, color: "#cbd5e1" }}>
            Filters
          </Typography>

          <TextField
            label="Company Name"
            variant="outlined"
            fullWidth
            value={filters.company_name}
            onChange={handleChange("company_name")}
            sx={{ mb: 2,
                  color: "#fff",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": { borderColor: "#475569" },
                    "&:hover fieldset": { borderColor: "#475569" },
                    "&.Mui-focused fieldset": { borderColor: "#475569" },
                  },
                  "& .MuiInputLabel-root": { color: "#94a3b8" },
             }}
          />
          <TextField
            label="Company Code"
            variant="outlined"
            fullWidth
            value={filters.company_code}
            onChange={handleChange("company_code")}
            sx={{ mb: 2,
                  color: "#fff",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": { borderColor: "#475569" },
                    "&:hover fieldset": { borderColor: "#475569" },
                    "&.Mui-focused fieldset": { borderColor: "#475569" },
                  },
                  "& .MuiInputLabel-root": { color: "#94a3b8" },
             }}
          />
          <Button
            variant="outlined"
            color="#fff"
            fullWidth
            sx={{ mt: 1, color: "#fff", borderColor: "#475569", "&:hover": { borderColor: "#475569" } }}
            onClick={() =>
              setFilters({
                company_name: "",
                company_code: "",
                billing_address: "",
                shipping_address: "",
                contact_email: "",
                phone: "",
              })
            }
          >
            Clear Filters
          </Button>

        </Box>

        {/* Supplier Table */}
        <Box sx={{ flex: 1, minWidth: 0, overflowX: "auto", top: 100 }}>
          <SupplierTable tableStyling={tableStyling} {...filters} />
        </Box>
      </div>
  );
}
