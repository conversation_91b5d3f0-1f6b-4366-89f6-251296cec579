import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import PageLayout from "../components/layout/PageLayout";
import PermissionRoutes from "../components/users/PermissionRoutes";

import Login       from "../pages/auth/Login";
import Logout      from "../pages/auth/Logout";
import Forbidden   from "../pages/utilities/Forbidden";
import Landing     from "../pages/Landing";
import Home        from "../pages/Home";
import Operations  from "../pages/operations/Operations";
import Business    from "../pages/business/Business";
import Suppliers   from "../pages/business/suppliers/Suppliers";
import Settings    from "../pages/admin/Settings";
import Users       from "../pages/users/Users";
import Permissions from "../pages/permissions/Permissions";
import Logs        from "../pages/utilities/Logs";

export default function AppRouter() {
  return (
    <Routes>
      <Route path="/login"     element={<Login />} />
      <Route path="/logout"    element={<Logout />} />
      <Route path="/forbidden" element={<Forbidden />} />

      <Route path="/" element={<Landing />} />
      <Route path="/landing" element={<Landing />} />

      <Route element={<PageLayout />}>
        <Route path="home"    element={<Home />} />

        <Route
          path="operations"
          element={
            <PermissionRoutes module="Operations" action="view">
              <Operations />
            </PermissionRoutes>
          }
        />

        <Route
          path="business"
          element={
            <PermissionRoutes module="Business" action="view">
              <Business />
            </PermissionRoutes>
          }
        />

        <Route
          path="suppliers"
          element={
            <PermissionRoutes module="Suppliers" action="view">
              <Suppliers />
            </PermissionRoutes>
          }
        />

        <Route
          path="settings/*"
          element={
            <PermissionRoutes module="Settings" action="view">
              <Settings />
            </PermissionRoutes>
          }
        />

        <Route
          path="users"
          element={
            <PermissionRoutes module="Users" action="view">
              <Users />
            </PermissionRoutes>
          }
        />

        <Route
          path="permissions"
          element={
            <PermissionRoutes module="Permissions" action="view">
              <Permissions />
            </PermissionRoutes>
          }
        />

        <Route
          path="logs"
          element={
            <PermissionRoutes module="Settings" action="view">
              <Logs />
            </PermissionRoutes>
          }
        />

        <Route path="*" element={<Navigate to="home" replace />} />
      </Route>

      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}
