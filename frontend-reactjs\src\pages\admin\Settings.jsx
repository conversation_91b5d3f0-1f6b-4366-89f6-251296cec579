import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import API from "../../services/api";
import toast from "react-hot-toast";

export default function Settings() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        await API.get("/settings");
      } catch (error) {
        toast.error("Access denied or server error.");
        navigate(error?.response?.status === 403 ? "/forbidden" : "/login");
      } finally {
        setLoading(false);
      }
    };
    fetchStats();
  }, [navigate]);

  const handleLogout = async () => {
    try {
      await API.post("/auth/logout");
      localStorage.removeItem("token");
      navigate("/login");
    } catch {
      toast.error("Logout failed.");
    }
  };

  const cards = [
    { key: "back", title: "Go Back", desc: "Return to the previous page.", action: () => navigate(-1) },
    { key: "users", title: "Users", desc: "Manage application users, roles, and account settings.", route: "/users" },
    { key: "permissions", title: "Permissions", desc: "Control role-based access to different modules.", route: "/permissions" },
    { key: "logs", title: "Logs", desc: "View recent system activity and change logs.", route: "/logs" },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-purple-400 text-xl font-mono animate-pulse">
          Loading Panel...
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white">
      {/* <Navbar handleLogout={handleLogout} /> */}

      <div className="max-w-7xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-semibold mb-10 text-white text-center">
          System Settings
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
          {cards.map(({ key, title, desc, route, action }) => (
            <div
              key={key}
              onClick={() => (action ? action() : navigate(route))}
              className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex flex-col justify-start h-full min-h-[220px] transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-purple-500"
            >
              <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
              <p className="text-gray-400 text-sm">{desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}


