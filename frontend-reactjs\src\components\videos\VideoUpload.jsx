import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  FormControlLabel,
  Switch,
  Chip,
  Stack
} from '@mui/material';
import { CloudUpload, VideoFile } from '@mui/icons-material';
import API from '../../services/api';
import toast from 'react-hot-toast';

const VideoUpload = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    is_public: false
  });

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'video/mp4',
        'video/avi',
        'video/quicktime',
        'video/webm',
        'video/ogg',
        'video/3gpp',
        'video/x-ms-wmv',
        'video/x-flv',
        'video/mkv',
        'video/x-matroska'
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error('Unsupported video format. Please select a valid video file.');
        return;
      }

      // Check file size (500MB limit)
      const maxSize = 500 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('File too large. Maximum size is 500MB.');
        return;
      }

      setSelectedFile(file);
      
      // Auto-fill title if empty
      if (!formData.title) {
        const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
        setFormData(prev => ({ ...prev, title: fileName }));
      }
    }
  };

  const handleInputChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a video file');
      return;
    }

    if (!formData.title.trim()) {
      toast.error('Please enter a title');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const uploadData = new FormData();
      uploadData.append('file', selectedFile);
      uploadData.append('title', formData.title.trim());
      uploadData.append('description', formData.description.trim());
      uploadData.append('category', formData.category.trim());
      uploadData.append('tags', formData.tags.trim());
      uploadData.append('is_public', formData.is_public);

      const response = await API.post('/videos/upload', uploadData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(progress);
        },
      });

      toast.success('Video uploaded successfully!');
      
      // Reset form
      setSelectedFile(null);
      setFormData({
        title: '',
        description: '',
        category: '',
        tags: '',
        is_public: false
      });
      setUploadProgress(0);

      // You might want to redirect or refresh a video list here
      console.log('Upload response:', response.data);

    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error.response?.data?.detail || 'Failed to upload video');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ color: '#f8fafc', mb: 3 }}>
        Upload Video
      </Typography>

      <Paper 
        sx={{ 
          p: 3, 
          backgroundColor: '#0f1419',
          border: '2px solid #1a5a7a',
          borderRadius: '1.2rem'
        }}
      >
        {/* File Upload Area */}
        <Box
          sx={{
            border: '2px dashed #1a5a7a',
            borderRadius: 2,
            p: 4,
            textAlign: 'center',
            mb: 3,
            backgroundColor: selectedFile ? '#10466b22' : 'transparent',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: '#10466b33',
              borderColor: '#4a9eff'
            }
          }}
          onClick={() => document.getElementById('video-upload').click()}
        >
          <input
            id="video-upload"
            type="file"
            accept="video/*"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />
          
          {selectedFile ? (
            <Stack spacing={2} alignItems="center">
              <VideoFile sx={{ fontSize: 48, color: '#4a9eff' }} />
              <Typography variant="h6" sx={{ color: '#f8fafc' }}>
                {selectedFile.name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#94a3b8' }}>
                {formatFileSize(selectedFile.size)}
              </Typography>
              <Button variant="outlined" size="small">
                Change File
              </Button>
            </Stack>
          ) : (
            <Stack spacing={2} alignItems="center">
              <CloudUpload sx={{ fontSize: 48, color: '#94a3b8' }} />
              <Typography variant="h6" sx={{ color: '#f8fafc' }}>
                Click to select video file
              </Typography>
              <Typography variant="body2" sx={{ color: '#94a3b8' }}>
                Supported formats: MP4, AVI, MOV, WebM, MKV (Max 500MB)
              </Typography>
            </Stack>
          )}
        </Box>

        {/* Form Fields */}
        <Stack spacing={3}>
          <TextField
            label="Title"
            value={formData.title}
            onChange={handleInputChange('title')}
            required
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#131a22',
                '& fieldset': { borderColor: '#1a5a7a' },
                '&:hover fieldset': { borderColor: '#4a9eff' },
                '&.Mui-focused fieldset': { borderColor: '#4a9eff' }
              },
              '& .MuiInputLabel-root': { color: '#94a3b8' },
              '& .MuiOutlinedInput-input': { color: '#f8fafc' }
            }}
          />

          <TextField
            label="Description"
            value={formData.description}
            onChange={handleInputChange('description')}
            multiline
            rows={3}
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#131a22',
                '& fieldset': { borderColor: '#1a5a7a' },
                '&:hover fieldset': { borderColor: '#4a9eff' },
                '&.Mui-focused fieldset': { borderColor: '#4a9eff' }
              },
              '& .MuiInputLabel-root': { color: '#94a3b8' },
              '& .MuiOutlinedInput-input': { color: '#f8fafc' }
            }}
          />

          <TextField
            label="Category"
            value={formData.category}
            onChange={handleInputChange('category')}
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#131a22',
                '& fieldset': { borderColor: '#1a5a7a' },
                '&:hover fieldset': { borderColor: '#4a9eff' },
                '&.Mui-focused fieldset': { borderColor: '#4a9eff' }
              },
              '& .MuiInputLabel-root': { color: '#94a3b8' },
              '& .MuiOutlinedInput-input': { color: '#f8fafc' }
            }}
          />

          <TextField
            label="Tags (comma-separated)"
            value={formData.tags}
            onChange={handleInputChange('tags')}
            fullWidth
            helperText="e.g., tutorial, demo, presentation"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#131a22',
                '& fieldset': { borderColor: '#1a5a7a' },
                '&:hover fieldset': { borderColor: '#4a9eff' },
                '&.Mui-focused fieldset': { borderColor: '#4a9eff' }
              },
              '& .MuiInputLabel-root': { color: '#94a3b8' },
              '& .MuiOutlinedInput-input': { color: '#f8fafc' },
              '& .MuiFormHelperText-root': { color: '#94a3b8' }
            }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={formData.is_public}
                onChange={handleInputChange('is_public')}
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: '#4a9eff',
                    '&:hover': { backgroundColor: '#4a9eff22' }
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: '#4a9eff'
                  }
                }}
              />
            }
            label={
              <Typography sx={{ color: '#f8fafc' }}>
                Make video public
              </Typography>
            }
          />
        </Stack>

        {/* Upload Progress */}
        {uploading && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" sx={{ color: '#94a3b8', mb: 1 }}>
              Uploading... {uploadProgress}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={uploadProgress}
              sx={{
                backgroundColor: '#131a22',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#4a9eff'
                }
              }}
            />
          </Box>
        )}

        {/* Upload Button */}
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
            sx={{
              backgroundColor: '#10466b',
              color: '#f8fafc',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              '&:hover': {
                backgroundColor: '#1a5a7a'
              },
              '&:disabled': {
                backgroundColor: '#131a22',
                color: '#94a3b8'
              }
            }}
          >
            {uploading ? 'Uploading...' : 'Upload Video'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default VideoUpload;
