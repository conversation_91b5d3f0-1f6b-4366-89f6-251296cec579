from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, Body, Request
from fastapi.responses import StreamingResponse, FileResponse
from sqlmodel import Session, select, func, desc, asc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from datetime import datetime, timezone
import os
import shutil
import mimetypes
from uuid import uuid4
import logging

from database.database import get_db
from models.video.video import Video, VideoMetadata, VideoView
from schemas.videos.video import (
    VideoCreate, VideoUpdate, VideoOut, VideoListOut, VideoUploadResponse,
    VideoStreamInfo, VideoSearchParams, VideoViewCreate
)
from routers.auth.auth import get_current_user
from routers.permissions.permissions import require_permission
from utilities.uploads.upload_paths import VIDEOS_DIR, UPLOADS_BASE

router = APIRouter(prefix="/videos", tags=["Videos"])

logger = logging.getLogger("videos")

# Supported video MIME types
ALLOWED_VIDEO_TYPES = {
    "video/mp4",
    "video/avi", 
    "video/quicktime",  # .mov
    "video/x-msvideo",  # .avi
    "video/webm",
    "video/ogg",
    "video/3gpp",       # .3gp
    "video/x-ms-wmv",   # .wmv
    "video/x-flv",      # .flv
    "video/mkv",
    "video/x-matroska", # .mkv
}

# Maximum file size (500MB)
MAX_FILE_SIZE = 500 * 1024 * 1024

@contextmanager
def atomic_transaction(session: Session):
    try:
        yield
        session.commit()
    except:
        session.rollback()
        raise


def get_video_metadata(file_path: str) -> Dict[str, Any]:
    """
    Extract basic video metadata. In production, you'd use ffprobe or similar.
    For now, we'll return basic file information.
    """
    try:
        file_size = os.path.getsize(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Basic format detection
        format_map = {
            '.mp4': 'mp4',
            '.avi': 'avi', 
            '.mov': 'quicktime',
            '.webm': 'webm',
            '.mkv': 'matroska',
            '.wmv': 'wmv',
            '.flv': 'flv',
            '.3gp': '3gpp',
            '.ogg': 'ogg'
        }
        
        return {
            'file_size': file_size,
            'format': format_map.get(file_ext, 'unknown'),
            'duration': None,  # Would need ffprobe
            'width': None,     # Would need ffprobe
            'height': None,    # Would need ffprobe
            'bitrate': None,   # Would need ffprobe
            'frame_rate': None, # Would need ffprobe
            'codec': None      # Would need ffprobe
        }
    except Exception as e:
        logger.error(f"Error extracting video metadata: {e}")
        return {
            'file_size': 0,
            'format': 'unknown',
            'duration': None,
            'width': None,
            'height': None,
            'bitrate': None,
            'frame_rate': None,
            'codec': None
        }


@router.post("/upload", response_model=VideoUploadResponse)
async def upload_video(
    file: UploadFile = File(...),
    title: str = Body(...),
    description: str = Body(""),
    is_public: bool = Body(False),
    tags: str = Body(""),
    category: str = Body(""),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload a video file"""
    
    # Validate file type
    if file.content_type not in ALLOWED_VIDEO_TYPES:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported video format. Allowed types: {', '.join(ALLOWED_VIDEO_TYPES)}"
        )
    
    # Check file size (read first chunk to estimate)
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Reset file pointer
    await file.seek(0)
    
    # Generate unique filename
    today = datetime.utcnow().strftime("%Y-%m-%d")
    uuid_str = str(uuid4())
    original_name, ext = os.path.splitext(file.filename)
    filename = f"{original_name}-{uuid_str}{ext}"
    
    # Create directory structure: uploads/videos/{user_id}/{YYYY-MM-DD}/
    save_dir = os.path.join(VIDEOS_DIR, str(current_user.id), today)
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, filename)
    
    try:
        # Save file
        with open(save_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Extract metadata
        metadata = get_video_metadata(save_path)
        
        # Get relative path for URL serving
        relative_path = save_path.replace(UPLOADS_BASE, "").replace("\\", "/")
        if relative_path.startswith("/"):
            relative_path = relative_path[1:]
        
        # Create video record
        with atomic_transaction(db):
            video = Video(
                title=title.strip(),
                description=description.strip(),
                filename=filename,
                original_filename=file.filename,
                file_url=f"/uploads/{relative_path}",
                file_size=metadata['file_size'],
                duration=metadata['duration'],
                width=metadata['width'],
                height=metadata['height'],
                format=metadata['format'],
                codec=metadata['codec'],
                bitrate=metadata['bitrate'],
                frame_rate=metadata['frame_rate'],
                uploaded_by=current_user.id,
                uploaded_at=datetime.utcnow(),
                is_public=is_public,
                is_processed=True,  # Set to True since we're not doing processing yet
                tags=tags.strip(),
                category=category.strip()
            )
            
            db.add(video)
            db.flush()  # Get the video ID
            db.refresh(video)
        
        logger.info(f"Video uploaded successfully: {video.id} - {video.title}")
        
        return VideoUploadResponse(
            id=video.id,
            title=video.title,
            file_url=video.file_url,
            message="Video uploaded successfully"
        )
        
    except Exception as e:
        # Clean up file if database operation fails
        if os.path.exists(save_path):
            os.remove(save_path)
        logger.error(f"Error uploading video: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload video: {str(e)}")


@router.get("/", response_model=List[VideoListOut])
async def list_videos(
    search_params: VideoSearchParams = Depends(),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List videos with search and filtering"""
    
    query = select(Video)
    
    # Apply filters
    if search_params.query:
        search_term = f"%{search_params.query}%"
        query = query.where(
            (Video.title.ilike(search_term)) |
            (Video.description.ilike(search_term)) |
            (Video.tags.ilike(search_term))
        )
    
    if search_params.category:
        query = query.where(Video.category == search_params.category)
    
    if search_params.is_public is not None:
        query = query.where(Video.is_public == search_params.is_public)
    
    if search_params.uploaded_by:
        query = query.where(Video.uploaded_by == search_params.uploaded_by)
    
    # Apply sorting
    sort_column = getattr(Video, search_params.sort_by)
    if search_params.sort_order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # Apply pagination
    query = query.offset(search_params.offset).limit(search_params.limit)
    
    videos = db.exec(query).all()
    
    # Convert to response format
    result = []
    for video in videos:
        # Get view count (you might want to cache this)
        view_count = db.exec(
            select(func.count(VideoView.id)).where(VideoView.video_id == video.id)
        ).first() or 0
        
        result.append(VideoListOut(
            id=video.id,
            title=video.title,
            description=video.description,
            thumbnail_url=video.thumbnail_url,
            duration=video.duration,
            file_size=video.file_size,
            uploaded_at=video.uploaded_at,
            is_public=video.is_public,
            category=video.category,
            view_count=view_count
        ))
    
    return result


@router.get("/{video_id}", response_model=VideoOut)
async def get_video(
    video_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video details by ID"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Check permissions - users can only see their own videos or public videos
    if not video.is_public and video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return VideoOut.model_validate(video)


@router.get("/{video_id}/stream")
async def stream_video(
    video_id: int,
    request: Request,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Stream video with range request support"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Check permissions
    if not video.is_public and video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Construct full file path
    file_path = os.path.join(UPLOADS_BASE, video.file_url.lstrip('/uploads/'))

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Video file not found")

    # Record view
    try:
        view = VideoView(
            video_id=video_id,
            viewer_ip=request.client.host,
            user_id=current_user.id,
            viewed_at=datetime.now(timezone.utc)
        )
        db.add(view)
        db.commit()
    except Exception as e:
        logger.warning(f"Failed to record video view: {e}")

    # Get file info
    file_size = os.path.getsize(file_path)

    # Handle range requests for video streaming
    range_header = request.headers.get('range')

    if range_header:
        # Parse range header (e.g., "bytes=0-1023")
        try:
            range_match = range_header.replace('bytes=', '').split('-')
            start = int(range_match[0]) if range_match[0] else 0
            end = int(range_match[1]) if range_match[1] else file_size - 1

            # Ensure valid range
            start = max(0, start)
            end = min(file_size - 1, end)
            content_length = end - start + 1

            def iter_file_range():
                with open(file_path, 'rb') as f:
                    f.seek(start)
                    remaining = content_length
                    while remaining > 0:
                        chunk_size = min(8192, remaining)  # 8KB chunks
                        chunk = f.read(chunk_size)
                        if not chunk:
                            break
                        remaining -= len(chunk)
                        yield chunk

            # Determine content type
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                content_type = "video/mp4"  # Default fallback

            headers = {
                'Content-Range': f'bytes {start}-{end}/{file_size}',
                'Accept-Ranges': 'bytes',
                'Content-Length': str(content_length),
                'Content-Type': content_type,
            }

            return StreamingResponse(
                iter_file_range(),
                status_code=206,  # Partial Content
                headers=headers
            )

        except (ValueError, IndexError):
            # Invalid range header, fall back to full file
            pass

    # Return full file if no range request or invalid range
    content_type, _ = mimetypes.guess_type(file_path)
    if not content_type:
        content_type = "video/mp4"

    def iter_file():
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(8192)  # 8KB chunks
                if not chunk:
                    break
                yield chunk

    headers = {
        'Content-Length': str(file_size),
        'Accept-Ranges': 'bytes',
        'Content-Type': content_type,
    }

    return StreamingResponse(iter_file(), headers=headers)


@router.put("/{video_id}", response_model=VideoOut)
async def update_video(
    video_id: int,
    video_update: VideoUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update video metadata"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Only the uploader can update the video
    if video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update fields
    update_data = video_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(video, field, value)

    video.updated_at = datetime.now(timezone.utc)

    try:
        db.add(video)
        db.commit()
        db.refresh(video)
        return VideoOut.model_validate(video)
    except Exception as e:
        logger.error(f"Error updating video {video_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update video")


@router.delete("/{video_id}")
async def delete_video(
    video_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a video and its file"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Only the uploader can delete the video
    if video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Construct file path
    file_path = os.path.join(UPLOADS_BASE, video.file_url.lstrip('/uploads/'))

    try:
        with atomic_transaction(db):
            # Delete related records first
            db.exec(select(VideoView).where(VideoView.video_id == video_id)).delete()
            db.exec(select(VideoMetadata).where(VideoMetadata.video_id == video_id)).delete()

            # Delete video record
            db.delete(video)

        # Delete physical file
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"Deleted video file: {file_path}")

        return {"message": "Video deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting video {video_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete video")


@router.get("/{video_id}/info", response_model=VideoStreamInfo)
async def get_video_stream_info(
    video_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video streaming information"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Check permissions
    if not video.is_public and video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return VideoStreamInfo(
        id=video.id,
        title=video.title,
        file_url=video.file_url,
        duration=video.duration,
        width=video.width,
        height=video.height,
        format=video.format,
        file_size=video.file_size
    )


@router.post("/{video_id}/view")
async def record_video_view(
    video_id: int,
    view_data: VideoViewCreate,
    request: Request,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Record a video view with watch duration"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Check permissions
    if not video.is_public and video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        view = VideoView(
            video_id=video_id,
            viewer_ip=request.client.host,
            user_id=current_user.id,
            viewed_at=datetime.now(timezone.utc),
            watch_duration=view_data.watch_duration
        )
        db.add(view)
        db.commit()

        return {"message": "View recorded successfully"}

    except Exception as e:
        logger.error(f"Error recording video view: {e}")
        raise HTTPException(status_code=500, detail="Failed to record view")


@router.get("/categories/list")
async def get_video_categories(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get list of video categories"""

    categories = db.exec(
        select(Video.category)
        .where(Video.category != "")
        .where(Video.category.isnot(None))
        .distinct()
    ).all()

    return {"categories": [cat for cat in categories if cat]}


@router.get("/stats/{video_id}")
async def get_video_stats(
    video_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video statistics"""

    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Only the uploader can see detailed stats
    if video.uploaded_by != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get view statistics
    total_views = db.exec(
        select(func.count(VideoView.id)).where(VideoView.video_id == video_id)
    ).first() or 0

    unique_viewers = db.exec(
        select(func.count(func.distinct(VideoView.user_id)))
        .where(VideoView.video_id == video_id)
        .where(VideoView.user_id.isnot(None))
    ).first() or 0

    avg_watch_duration = db.exec(
        select(func.avg(VideoView.watch_duration))
        .where(VideoView.video_id == video_id)
        .where(VideoView.watch_duration.isnot(None))
    ).first() or 0

    return {
        "video_id": video_id,
        "total_views": total_views,
        "unique_viewers": unique_viewers,
        "average_watch_duration": float(avg_watch_duration) if avg_watch_duration else 0,
        "file_size": video.file_size,
        "duration": video.duration,
        "uploaded_at": video.uploaded_at
    }
