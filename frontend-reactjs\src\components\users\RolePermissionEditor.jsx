import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
} from '@mui/material';
import API from '../../services/api';
import toast from 'react-hot-toast';

export default function RolePermissionEditor({
  initialRole,
  onClose = () => {},
  onSaved = () => {},
}) {
  const [roles, setRoles] = useState([]);
  const [modules, setModules] = useState([]);
  const [actions, setActions] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState('');
  const [selectedModule, setSelectedModule] = useState('');
  const [loading, setLoading] = useState(true);

  // Fetch roles & modules once on mount
  useEffect(() => {
    setLoading(true);
    Promise.all([API.get('/auth/roles'), API.get('/auth/modules')])
      .then(([rRes, mRes]) => {
        setRoles(rRes.data);
        setModules(mRes.data);
        setSelectedRole(initialRole || rRes.data[0] || '');
        setSelectedModule(mRes.data[0]?.name || '');
      })
      .catch(() => toast.error('Failed to load permissions data'))
      .finally(() => setLoading(false));
  }, [initialRole]);

  // Fetch permissions for current role+module
  useEffect(() => {
    if (!selectedRole || !selectedModule) {
      setPermissions([]);
      setActions([]);
      return;
    }
    setLoading(true);
    // Get actions for this module
    const moduleObj = modules.find((m) => m.name === selectedModule);
    const acts = moduleObj?.actions || [];
    setActions(acts);

    // Fetch backend permissions for current role/module
    API.get(`/auth/permissions/${selectedRole}/${selectedModule}`)
      .then((res) => {
        // Make sure every action has a row
        const merged = acts.map((action) => {
          const found = res.data.find((p) => p.action === action);
          return {
            role: selectedRole,
            module: selectedModule,
            action,
            allowed: found ? found.allowed : false,
          };
        });
        setPermissions(merged);
      })
      .catch(() => setPermissions([]))
      .finally(() => setLoading(false));
  }, [selectedRole, selectedModule, modules]);

  // Toggle permission locally
  const toggleLocal = (action) => {
    setPermissions((prev) =>
      prev.map((p) =>
        p.action === action
          ? { ...p, allowed: !p.allowed }
          : p
      )
    );
  };

  // Save permissions (PUT each action)
  const handleSave = async () => {
    setLoading(true);
    try {
      await Promise.all(
        permissions.map((p) =>
          API.put('/auth/permissions', {
            role: selectedRole,
            module: selectedModule,
            action: p.action,
            allowed: p.allowed,
          })
        )
      );
      toast.success('Permissions saved');
      // Re-fetch from backend to update with latest saved
      const { data: freshPerms } = await API.get(
        `/auth/permissions/${selectedRole}/${selectedModule}`
      );
      setPermissions(
        actions.map((action) => {
          const found = freshPerms.find((p) => p.action === action);
          return {
            role: selectedRole,
            module: selectedModule,
            action,
            allowed: found ? found.allowed : false,
          };
        })
      );
      onSaved();
      onClose();
    } catch {
      toast.error('Failed to save permissions');
    } finally {
      setLoading(false);
    }
  };

  // Styling (unchanged)
  const selectSx = {
    color: 'white',
    '.MuiSvgIcon-root': { color: 'white' },
    '& .MuiSelect-select': { backgroundColor: '#1f2937' },
    '& .MuiInputLabel-root': { color: 'white' },
    '& .Mui-focused .MuiInputLabel-root': { color: 'white' },
  };
  const menuProps = {
    PaperProps: { sx: { bgcolor: '#1f2937', color: 'white' } },
    MenuListProps: {
      sx: {
        '& .MuiMenuItem-root': { color: 'white' },
        '& .MuiMenuItem-root.Mui-selected': { bgcolor: '#374151' },
        '& .MuiMenuItem-root:hover': { bgcolor: '#4b5563' },
      },
    },
  };

  return (
    <Box className="p-6 max-w-3xl mx-auto bg-gray-900 border border-gray-800 rounded-2xl shadow-md text-white space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormControl autoComplete="off" sx={{ bgcolor: 'gray.800', borderRadius: 1 }}>
          <InputLabel id="role-select-label" sx={{ color: 'white' }}>
            Role
          </InputLabel>
          <Select
            labelId="role-select-label"
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            sx={selectSx}
            MenuProps={menuProps}
          >
            {roles.map((r) => (
              <MenuItem key={r} value={r}>
                {r.charAt(0).toUpperCase() + r.slice(1)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl autoComplete="off" sx={{ bgcolor: 'gray.800', borderRadius: 1 }}>
          <InputLabel id="module-select-label" sx={{ color: 'white' }}>
            Module
          </InputLabel>
          <Select
            labelId="module-select-label"
            value={selectedModule}
            onChange={(e) => setSelectedModule(e.target.value)}
            sx={selectSx}
            MenuProps={menuProps}
          >
            {modules.map((m) => (
              <MenuItem key={m.name} value={m.name}>
                {m.name.charAt(0).toUpperCase() + m.name.slice(1)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>

      {actions.length > 0 && (
        <Box className="bg-gray-800 border border-gray-700 p-4 rounded-lg space-y-2">
          <Typography variant="subtitle1" className="text-gray-200">
            {selectedModule.charAt(0).toUpperCase() +
              selectedModule.slice(1)}{' '}
            Actions
          </Typography>
          <Divider className="border-gray-600" sx={{ mb: 2 }} />
          <FormGroup>
            {actions.map((action) => (
              <FormControlLabel autoComplete="off"
                key={action}
                control={
                  <Checkbox
                    checked={
                      permissions.find((p) => p.action === action)?.allowed ||
                      false
                    }
                    onChange={() => toggleLocal(action)}
                    sx={{
                      color: 'white',
                      '&.Mui-checked': {
                        color: 'green.400',
                      },
                    }}
                  />
                }
                label={
                  <span className="text-white text-sm">
                    {action.charAt(0).toUpperCase() + action.slice(1)}
                  </span>
                }
              />
            ))}
          </FormGroup>
        </Box>
      )}

      <div className="flex justify-end space-x-4 mt-4">
        <Button
          onClick={onClose}
          sx={{
            bgcolor: 'gray.600',
            '&:hover': { bgcolor: 'gray.500' },
            color: 'white',
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={loading}
          sx={{
            bgcolor: 'green.600',
            '&:hover': { bgcolor: 'green.500' },
            color: 'white',
          }}
        >
          Save
        </Button>
      </div>
    </Box>
  );
}
