import axios from "axios";
import toast from "react-hot-toast";

const API = axios.create({
  baseURL: "https://localhost:8000",
  withCredentials: true, 
});

let isRefreshing = false;

API.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !isRefreshing
    ) {
      originalRequest._retry = true;
      isRefreshing = true;

      try {
        await API.post("/auth/refresh");
        isRefreshing = false;
        return API(originalRequest); 
      } catch (refreshError) {
        toast.error("Session expired. Please log in again.");
        isRefreshing = false;
        window.location.href = "/login";
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default API;
