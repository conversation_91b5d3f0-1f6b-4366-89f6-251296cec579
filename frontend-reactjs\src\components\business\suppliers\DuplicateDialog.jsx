import React, { useState, useEffect } from "react";
import {
  <PERSON>alog, <PERSON>alogTitle, DialogContent, DialogActions, Typography, Table,
  TableHead, TableRow, TableCell, TableBody, Button, Radio,
  FormControlLabel, Box, IconButton, Collapse
} from "@mui/material";
import { KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import MergeWizard from "./MergeWizard";
import mergeFields from "./mergeFields";
import { cleanStr } from "../../utilities/json_validator";

export default function DuplicateDialog({
  open,
  preview,
  onClose,
  onCommit,
  setPreview,
  importFile,
}) {
  const duplicates = preview ? preview.filter(row => row.duplicates) : [];
  const [duplicateActions, setDuplicateActions] = useState([]);
  const [mergeRowIdx, setMergeRowIdx] = useState(null);
  const [mergeSelections, setMergeSelections] = useState({});
  const [expandedRows, setExpandedRows] = useState({});
  
  useEffect(() => {
    setDuplicateActions(duplicates.map((row, i) =>
      (mergeSelections[i] ? "merge" : (row.action || "skip"))
    ));
    setMergeRowIdx(null);
    setExpandedRows({});
  }, [open, preview]);

  const handleDuplicateActionChange = (idx, value) => {
    setDuplicateActions(actions => actions.map((a, i) => (i === idx ? value : a)));
    if (value !== "merge" && mergeSelections[idx]) {
      setMergeSelections(ms => {
        const copy = { ...ms };
        delete copy[idx];
        return copy;
      });
    }
  };

  const handleSetAllActions = (action) => {
    setDuplicateActions(duplicates.map(() => action));
  };

  const getMergedData = (existing, imported, choices) => {
    const merged = {};
    mergeFields.forEach(field => {
      const key = field.key;
      if (choices && choices[key] === "imported") {
        merged[key] = imported[key] ?? "";
      } else {
        merged[key] = existing[key] ?? "";
      }
    });
    return merged;
  };

  const handleImport = () => {
    const actions = preview.map((row, idx) => {
      const dupIdx = duplicates.findIndex(
        d =>
          d.imported.company_name === row.imported.company_name &&
          d.imported.company_code === row.imported.company_code
      );
      let action, merge_fields, merged_values;
  
      if (dupIdx >= 0) {
        if (mergeSelections[dupIdx]) {
          action = "merge";
          merge_fields = { ...mergeSelections[dupIdx] };
          merged_values = getMergedData(
            duplicates[dupIdx].existing,
            duplicates[dupIdx].imported,
            mergeSelections[dupIdx]
          );
        } else if (duplicateActions[dupIdx] === "overwrite") {
          action = "overwrite";
        } else {
          action = "skip";
        }
      } else {
        action = "import";
      }
  
      return {
        ...row,
        action,
        ...(action === "merge" ? merged_values : {}),
      };
    });
    onCommit(actions, importFile);
  };

  const saveMergeChoices = (idx, choices) => {
    setMergeSelections(ms => ({
      ...ms,
      [idx]: choices
    }));
    setDuplicateActions(actions => actions.map((act, i) => (i === idx ? "merge" : act)));
  };

  const openMergeWizard = idx => setMergeRowIdx(idx);
  const closeMergeWizard = () => setMergeRowIdx(null);

  // Helper for field value
  function getFieldValue(obj, field, isImported = false) {
    if (!obj) return "";
  
    // Flat structure for imported
    if (isImported) {
      return obj[field.key] ?? "";
    }
  
    // Profile fields (for payment_terms, credit_limit, etc)
    if (field.nested === "profile") {
      return obj.profile?.[field.key] ?? "";
    }
  
    // Contact fields
    if (field.nested === "contacts") {
      // Find the primary contact
      const contact =
        Array.isArray(obj.contacts) &&
        (obj.contacts.find((c) => c.is_primary) || obj.contacts[0]);
      if (!contact) return "";
      switch (field.key) {
        case "primary_contact_name":
          return contact.name ?? "";
        case "primary_contact_email":
          return contact.email ?? "";
        case "primary_contact_phone":
          return contact.phone ?? "";
        case "primary_contact_title":
          return contact.title ?? "";
        default:
          return "";
      }
    }
  
    // Address fields
    if (field.nested === "addresses") {
      const type = field.type; // should be "billing" or "shipping"
      // Find address by type (billing/shipping)
      const address =
        Array.isArray(obj.addresses) &&
        obj.addresses.find((a) => a.type === type);
      if (!address) return "";
      // Map UI field to address property
      const fieldMap = {
        billing_street_address: "street_address",
        billing_city: "city",
        billing_state: "state",
        billing_zipcode: "zipcode",
        billing_phone: "phone",
        billing_contact_email: "contact_email",
        billing_notes: "notes",
        shipping_street_address: "street_address",
        shipping_city: "city",
        shipping_state: "state",
        shipping_zipcode: "zipcode",
        shipping_phone: "phone",
        shipping_contact_email: "contact_email",
        shipping_notes: "notes",
      };
      return address[fieldMap[field.key]] ?? "";
    }
  
    // Fallback: direct property
    return obj[field.key] ?? "";
  }

  // Toggle expand/collapse for a row
  const handleExpandRow = idx => {
    setExpandedRows(rows => ({
      ...rows,
      [idx]: !rows[idx],
    }));
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth
        PaperProps={{
          sx: {
            bgcolor: "#10162a",
            color: "#e0e7ff",
            borderRadius: 3,
            border: "2px solid #334155",
            boxShadow: "0 8px 32px 0 #1e293b88"
          }
        }}>
        <DialogTitle
          sx={{
            bgcolor: "#181f36", color: "#a5b4fc", borderBottom: "1px solid #334155",
            fontWeight: 700, fontSize: "1.25rem", letterSpacing: 1, pb: 2, px: 4
          }}
        >
          Duplicate Suppliers Detected
        </DialogTitle>
        <DialogContent sx={{ bgcolor: "#10162a", color: "#e0e7ff", px: 4, pt: 2, pb: 0 }}>
          <Typography sx={{ color: "#fb7185", mb: 2, fontWeight: 600 }}>
            The following supplier(s) already exist (by company name and code). For each, choose <span style={{ color: "#4ade80" }}>"Skip"</span>, <span style={{ color: "#a5b4fc" }}>"Overwrite"</span>, or use the Merge Wizard for field-level merging.
          </Typography>
          <Table size="small" sx={{ bgcolor: "#1e293b", borderRadius: 2, border: "1.5px solid #334155", mb: 2 }}>
            <TableHead>
              <TableRow sx={{ bgcolor: "#232949" }}>
                <TableCell /> {/* expand arrow */}
                <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Company Name</TableCell>
                <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Company Code</TableCell>
                <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Action</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              {duplicates.map((dup, idx) => {
                // --- Helper: Is Merge Selected ---
                const sel = mergeSelections[idx];
                const choices = sel ? Object.values(sel) : [];
                const anyImported = choices.some(v => v === "imported");
                const allExisting = choices.length > 0 && choices.every(v => v === "existing");

                // --- Determine which radio to display as selected ---
                let selectedValue = duplicateActions[idx] || "skip";
                if (sel && anyImported) {
                  selectedValue = "merge";
                }
                if (sel && allExisting) {
                  selectedValue = "skip";
                }

                return (
                  <React.Fragment key={idx}>
                    <TableRow
                      sx={{
                        background: idx % 2 === 0 ? "#20263a" : "#232940",
                        "&:last-child td": { borderBottom: "none" }
                      }}>
                      <TableCell sx={{ width: 30 }}>
                        <IconButton
                          aria-label={expandedRows[idx] ? "Collapse" : "Expand"}
                          size="small"
                          onClick={() => handleExpandRow(idx)}
                          sx={{ color: "#a5b4fc" }}
                        >
                          {expandedRows[idx] ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                        </IconButton>
                      </TableCell>
                      <TableCell
                        sx={{ color: "#e0e7ff", borderBottom: "1px solid #334155", borderRight: "1px solid #334155" }}
                      >
                        {dup.imported?.company_name || ""}
                      </TableCell>
                      <TableCell
                        sx={{ color: "#e0e7ff", borderBottom: "1px solid #334155", borderRight: "1px solid #334155" }}
                      >
                        {dup.imported?.company_code || ""}
                      </TableCell>
                      <TableCell sx={{ borderBottom: "1px solid #334155" }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                          <FormControlLabel
                            value="skip"
                            control={<Radio />}
                            label={<span style={{ color: "#4ade80" }}>Skip</span>}
                            checked={selectedValue === "skip"}
                            onChange={() => handleDuplicateActionChange(idx, "skip")}
                            sx={{ mr: 3 }}
                          />
                          <FormControlLabel
                            value="overwrite"
                            control={<Radio />}
                            label={<span style={{ color: "#a5b4fc" }}>Overwrite</span>}
                            checked={selectedValue === "overwrite"}
                            onChange={() => handleDuplicateActionChange(idx, "overwrite")}
                            sx={{ mr: 3 }}
                          />
                          <FormControlLabel
                            value="merge"
                            control={<Radio />}
                            label={<span style={{ color: "#fbbf24" }}>Merge</span>}
                            checked={selectedValue === "merge"}
                            disabled
                            sx={{
                              "& .MuiFormControlLabel-label": {
                                color: "#fbbf24 !important",
                                opacity: 0.6,
                              },
                              mr: 0
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          sx={{
                            mt: 0,
                            fontSize: "0.8rem",
                            bgcolor: "#232949",
                            color: "#a5b4fc",
                            borderRadius: 2,
                            "&:hover": { bgcolor: "#6366f1", color: "#fff" }
                          }}
                          onClick={() => openMergeWizard(idx)}
                        >
                          Open Merge Wizard
                        </Button>
                      </TableCell>
                    </TableRow>
                    {/* Expandable diff row, keep your previous code here */}
                    <TableRow>
                      <TableCell
                        sx={{ p: 0, bgcolor: "#141a2d", border: "none" }}
                        colSpan={5}
                      >
                        <Collapse in={expandedRows[idx]} timeout="auto" unmountOnExit>
                          <Box sx={{ margin: 0, bgcolor: "#141a2d", p: 2, borderRadius: 2 }}>
                            <Typography sx={{ color: "#a5b4fc", mb: 1, fontWeight: 600 }}>
                              Field Differences
                            </Typography>
                            <Table size="small" sx={{ bgcolor: "#232940", borderRadius: 2, mb: 1 }}>
                              <TableHead>
                                <TableRow>
                                  <TableCell sx={{ color: "#a5b4fc" }}>Field</TableCell>
                                  <TableCell sx={{ color: "#a5b4fc" }}>Existing</TableCell>
                                  <TableCell sx={{ color: "#a5b4fc" }}>Imported</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {mergeFields.map(field => {
                                  const existingVal = getFieldValue(dup.existing, field);
                                  const importedVal = getFieldValue(dup.imported, field, true);
                                  const changed = (existingVal || "") !== (importedVal || "");
                                  return (
                                    <TableRow key={field.key}>
                                      <TableCell>{field.label}</TableCell>
                                      <TableCell sx={{ color: changed ? "#fbbf24" : "#94a3b8" }}>
                                        {existingVal !== "" ? existingVal : <i>(empty)</i>}
                                      </TableCell>
                                      <TableCell sx={{ color: changed ? "#4ade80" : "#e0e7ff" }}>
                                        {cleanStr(importedVal) !== "" ? cleanStr(importedVal) : <i>(empty)</i>}
                                      </TableCell>
                                    </TableRow>
                                  );
                                })}
                              </TableBody>
                            </Table>
                          </Box>
                        </Collapse>
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
          <Box mt={2}>
            <Typography variant="body2" sx={{ color: "#64748b", fontWeight: 400 }}>
              When ready, click <span style={{ color: "#a5b4fc", fontWeight: 600 }}>Import</span> to apply your choices.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            bgcolor: "#181f36", borderTop: "1px solid #334155", py: 2, px: 4, gap: 2,
            display: "flex", flexWrap: "wrap", justifyContent: "flex-end"
          }}>
          <Button
            onClick={() => handleSetAllActions("skip")}
            sx={{
              bgcolor: "#334155", color: "#e0e7ff", borderRadius: 2,
              fontWeight: 600, mb: 1, "&:hover": { bgcolor: "#475569" }
            }}
          >
            Skip All
          </Button>
          <Button
            onClick={() => handleSetAllActions("overwrite")}
            sx={{
              bgcolor: "#334155", color: "#e0e7ff", borderRadius: 2,
              fontWeight: 600, mb: 1, "&:hover": { bgcolor: "#818cf8" }
            }}
          >
            Overwrite All
          </Button>
          <Button
            onClick={handleImport}
            variant="contained"
            sx={{
              bgcolor: "#6366f1", color: "#e0e7ff", fontWeight: 600, borderRadius: 2,
              boxShadow: "0 0 8px 0 #6366f1bb", mr: 1, mb: 1, "&:hover": { bgcolor: "#818cf8" }
            }}>
            Import
          </Button>
          <Button
            onClick={onClose}
            sx={{
              bgcolor: "#334155", color: "#e0e7ff", borderRadius: 2, fontWeight: 600,
              mb: 1, "&:hover": { bgcolor: "#475569" }
            }}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      {mergeRowIdx !== null && (
        <MergeWizard
          open={mergeRowIdx !== null}
          duplicate={duplicates[mergeRowIdx].imported}
          existing={duplicates[mergeRowIdx].existing}
          imported={duplicates[mergeRowIdx].imported}
          mergeFields={mergeFields}
          mergeChoices={mergeSelections[mergeRowIdx] || {}}
          setMergeChoices={choices => saveMergeChoices(mergeRowIdx, choices)}
          onCancel={closeMergeWizard}
          onSave={() => closeMergeWizard()}
        />
      )}
    </>
  );
}

