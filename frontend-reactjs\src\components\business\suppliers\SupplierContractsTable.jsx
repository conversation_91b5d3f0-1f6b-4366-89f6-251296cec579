import React from "react";
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  IconButton, Tooltip, Typography, Box
} from "@mui/material";
import {
  Download as DownloadIcon,
  Visibility as EyeIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from "@mui/icons-material";

export default function SupplierContractsTable({ supplier, onDelete, onEdit }) {
  // Only show contracts with type === "contract"
  const contracts = (supplier?.documents || []).filter(doc => doc.type === "contract");

  if (!contracts.length) {
    return (
      <Box
        className="bg-gray-900 rounded-xl p-6 my-4 border border-indigo-700 shadow-lg flex items-center justify-center min-h-[100px]"
        sx={{
          background: "linear-gradient(135deg,#181f36 85%,#0f172a 100%)",
          borderRadius: 4,
          boxShadow: "0 8px 40px 0 #312e8199",
          my: 3,
          border: "1.5px solid #3730a3"
        }}
      >
        <Typography sx={{ color: "#64748b" }}>
          No contract files uploaded.
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer
      sx={{
        background: "linear-gradient(135deg,#181f36 85%,#0f172a 100%)",
        borderRadius: 4,
        boxShadow: "0 8px 40px 0 #312e8199",
        my: 3,
        border: "1.5px solid #3730a3"
      }}
    >
      <Table size="medium" sx={{
        "& th, & td": { color: "#e0e7ff", borderBottom: "1px solid #334155" },
        "& th": { fontWeight: 600, textTransform: "uppercase", letterSpacing: 1, background: "#232949" },
        "& tbody tr:hover": { background: "#1e293b", transition: "background .2s" }
      }}>
        <TableHead>
          <TableRow>
            <TableCell>Contract Name</TableCell>
            <TableCell>Contract Type</TableCell>
            <TableCell>Effective Date</TableCell>
            <TableCell>Expiration Date</TableCell>
            <TableCell align="center">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {contracts.map((contract, idx) => (
            <TableRow key={contract.id || idx}>
              <TableCell sx={{ fontWeight: 500 }}>
                <a
                  href={contract.file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: "#60a5fa",
                    textDecoration: "underline",
                    fontWeight: 600,
                    letterSpacing: 0.3
                  }}
                >
                  {contract.filename}
                </a>
              </TableCell>
              <TableCell>
                <span style={{
                  padding: "3px 10px",
                  borderRadius: "1rem",
                  background: "#3730a3",
                  color: "#a5b4fc",
                  fontWeight: 500,
                  fontSize: "0.95em"
                }}>
                  {contract.type
                    ? contract.type.charAt(0).toUpperCase() + contract.type.slice(1)
                    : "—"}
                </span>
              </TableCell>
              <TableCell>
                {contract.effective_date
                  ? new Date(contract.effective_date).toLocaleDateString()
                  : <span style={{ color: "#64748b" }}>—</span>}
              </TableCell>
              <TableCell>
                {contract.expiration_date
                  ? new Date(contract.expiration_date).toLocaleDateString()
                  : <span style={{ color: "#64748b" }}>—</span>}
              </TableCell>
              <TableCell align="center">
                {/* View */}
                <Tooltip title="View">
                  <IconButton
                    href={contract.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ color: "#60a5fa" }}
                  >
                    <EyeIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                {/* Download */}
                <Tooltip title="Download">
                  <IconButton
                    href={contract.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    download={contract.filename}
                    sx={{ color: "#4ade80" }}
                  >
                    <DownloadIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                {/* Edit (Replace) */}
                {onEdit && (
                  <Tooltip title="Edit (Replace)">
                    <IconButton
                      onClick={() => onEdit(contract)}
                      sx={{ color: "#a5b4fc" }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                {/* Delete */}
                {onDelete && (
                  <Tooltip title="Delete">
                    <IconButton
                      onClick={() => onDelete(contract)}
                      sx={{ color: "#fb7185" }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
