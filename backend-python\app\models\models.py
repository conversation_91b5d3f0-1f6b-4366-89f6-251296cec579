from sqlmodel import SQLModel, Field, Relationship, Column, String, Integer, <PERSON><PERSON><PERSON>
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

# Import all models to ensure they're registered with SQLModel
from models.users.users import User, UserRole
from models.supplier.supplier import Supplier, SupplierAddress, SupplierContact, SupplierDocument
from models.video.video import Video, VideoMetadata, VideoView

class Module(SQLModel, table=True):
    __tablename__ = "modules"
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(unique=True, index=True)
    perms: List["RolePermission"] = Relationship(back_populates="module")

class RolePermission(SQLModel, table=True):
    __tablename__ = "role_permissions"
    id: Optional[int] = Field(default=None, primary_key=True)
    role: str = Field(sa_column=Column(String(32), nullable=False, index=True))  # role as string
    module_id: int = Field(foreign_key="modules.id")
    action: str = Field(sa_column=Column(String(16), nullable=False))
    allowed: bool = Field(default=False)
    module: Module = Relationship(back_populates="perms")