import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import API from "../../services/api";
import toast from "react-hot-toast";

export default function Operations() {
  const navigate = useNavigate();
  const [activeCard, setActiveCard] = useState(null);

  const handleLogout = async () => {
    try {
      await API.post("/auth/logout");
      localStorage.removeItem("token");
      navigate("/login");
    } catch (err) {
      console.error("Logout failed", err);
      toast.error("Logout failed. Please try again.");
    }
  };

  const cards = [
    {
      key: "back",
      title: "Go Back",
      desc: "Return to the previous page.",
      action: () => navigate(-1),
    },
    {
      key: "suppliers",
      title: "Suppliers",
      desc: "Manage supplier information, addresses, contacts, and documents.",
      action: () => navigate("/suppliers"),
    },
  ];

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white">
      {/* <Navbar handleLogout={handleLogout} /> */}

      <div className="max-w-7xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-semibold mb-10 text-white text-center">Business Logic</h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
          {cards.map(({ key, title, desc, action }) => (
            <div
              key={key}
              onClick={action}
              className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex flex-col justify-start h-full min-h-[180px] transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-purple-500"
            >
              <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
              <p className="text-gray-400 text-sm">{desc}</p>
            </div>
          ))}
        </div>

        {activeCard && (
          <div className="mt-10 bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-lg">

          </div>
        )}
      </div>
    </div>
  );
}
