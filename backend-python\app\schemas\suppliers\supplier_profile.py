from pydantic import BaseModel, validator
from typing import Optional
from datetime import date

class SupplierProfileBase(BaseModel):
    payment_terms: Optional[str] = None
    credit_limit: Optional[float] = None
    credit_hold: bool = False
    sla_description: Optional[str] = None
    contract_url: Optional[str] = None
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None

    @validator('payment_terms', 'sla_description', 'contract_url', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()

    @validator('credit_limit', pre=True, always=True)
    def clean_credit_limit(cls, v):
        # Accept blank, "nan", None, NaN as None, else float.
        if v is None or (isinstance(v, str) and v.strip().lower() in {"", "nan", "nat", "none", "null"}):
            return None
        try:
            return float(v)
        except Exception:
            return None

    @validator('effective_date', 'expiration_date', pre=True, always=True)
    def clean_dates(cls, v):
        if not v or str(v).lower() in {"nan", "nat", "none", "null", ""}:
            return None
        if isinstance(v, date):
            return v
        try:
            # Accept common string formats for date
            from datetime import datetime
            return datetime.fromisoformat(str(v)).date()
        except Exception:
            return None

class SupplierProfileCreate(SupplierProfileBase):
    pass

class SupplierProfileRead(SupplierProfileBase):
    id: int

    class Config:
        from_attributes = True
