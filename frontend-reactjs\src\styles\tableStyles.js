// src/styles/tableStyles.js

// Dark Space Theme Colors
const HEADER_BG       = '#111827';
const BORDER_COLOR    = '#334155';
const ROW_EVEN        = '#111827';
const ROW_ODD         = '#1f2937';
const TEXT_COLOR      = '#fff';

export const tableStyling = {
  // Paper container
  muiTablePaperProps: {
    elevation: 0,
    sx: {
      background: HEADER_BG,
      borderRadius: '1rem',
      border: `1px solid ${BORDER_COLOR}`,
      boxShadow: '0 0.5rem 0.5rem rgba(0, 0, 0, 0.2)',
      overflow: 'hidden',
      width: '100%',
    },
  },
  // Scroll container
  muiTableContainerProps: {
    sx: {
      background: HEADER_BG,
      borderRadius: '1rem',
      boxShadow: '0 0.5rem 0.5rem rgba(0, 0, 0, 0.2)',
      overflow: 'auto',
      width: '100%',
    },
  },
  // Header cells
  muiTableHeadCellProps: () => ({
    sx: {
      background: HEADER_BG,
      color: TEXT_COLOR,
      fontWeight: 600,
      textTransform: 'uppercase',
      fontSize: '0.75rem',
      letterSpacing: '0.05em',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  }),
  // Body cells (striped)
  muiTableBodyCellProps: ({ cell }) => ({
    sx: {
      background: cell.row.index % 2 === 0 ? ROW_EVEN : ROW_ODD,
      color: TEXT_COLOR,
      py: 1,
      px: 2,
      height: '48px',
      maxHeight: '48px',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  }),
};

// Info-button toggle styles
export const infoIconButtonStyles = (isExpanded) => ({
    color: isExpanded ? 'cyan' : '#fff',
    fontSize: '1.2rem',
    p: 0.5,
    transition: 'color 0.2s',
  });

export const primaryNameCellStyle = {
    fontWeight: 700,
    textDecoration: 'underline',
    textDecorationColor: '#fff',
    cursor: 'pointer',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    fontSize: '0.85rem',
 };

export const wrapperBoxProps = {
sx: { width: '100%' },
};

export const topToolbarProps = {
sx: { backgroundColor: 'rgb(17, 24, 39)' },
};

export const bottomToolbarProps = {
sx: { backgroundColor: 'rgb(17, 24, 39)' },
};

export const paginationProps = {
sx: {
    // target the "1–10 of NNN" label
    '& .MuiTablePagination-displayedRows': {
    color: '#fff',
    fontWeight: 500,
    },
    // target the "Rows per page" label
    '& .MuiTablePagination-selectLabel': {
    color: '#fff',
    fontWeight: 500,
    },
    // target the "Next" and "Previous" buttons
    '& .MuiTablePagination-actions': {
    color: '#fff',
    },
  },
};