from sqlmodel import Session, select
from database.database import engine
from models.models import Module, RolePermission
from models.users.users import User, UserRole
from auth.auth import hash_password
from datetime import datetime

def seed():
    with Session(engine) as session:
        module_names = ["Users", "Permissions", "Settings", "Operations", "Business", "Suppliers"]
        modules = []
        for name in module_names:
            mod = session.exec(select(Module).where(Module.name == name)).first()
            if not mod:
                mod = Module(name=name)
                session.add(mod)
                session.commit()
            modules.append(mod)

        role_names = ["admin", "manager", "supervisor", "lead", "waiver", "replenish", "stow", "pick", "pack", "viewer", "user"]

        # Give all permissions to "admin" role as a string
        for mod in modules:
            for action in ["view", "create", "update", "delete"]:
                exists = session.exec(
                    select(RolePermission).where(
                        RolePermission.role == "admin",
                        RolePermission.module_id == mod.id,
                        RolePermission.action == action,
                    )
                ).first()
                if not exists:
                    session.add(RolePermission(
                        role="admin",
                        module_id=mod.id,
                        action=action,
                        allowed=True,
                    ))

        # Create the admin user if it doesn't exist
        user = session.exec(select(User).where(User.username == "admin")).first()
        if not user:
            user = User(
                username="admin",
                email="<EMAIL>",
                password_hash=hash_password("password"),
                last_login=datetime.utcnow(),
            )
            session.add(user)
            session.flush()  # get user.id

            # Assign the admin role (as string) to the user
            session.add(UserRole(user_id=user.id, role="admin"))
            print("Admin user created.")
        else:
            # If admin exists but email is missing or blank, update it
            if not getattr(user, "email", None):
                user.email = "<EMAIL>"
                session.add(user)
                print("Admin user email set.")

        session.commit()
        print("Seeded admin user, modules, roles, and permissions.")

if __name__ == "__main__":
    seed()
