FROM python:3.13-slim

WORKDIR /app

COPY requirements.txt .

# Install build and system dependencies first
RUN apt-get update && \
    apt-get install -y --no-install-recommends gcc libssl-dev libffi-dev && \
    rm -rf /var/lib/apt/lists/*

# Now install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

COPY app/ ./app/

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--ssl-keyfile", "/app/config/ssl/certs/localhost+2-key.pem", "--ssl-certfile", "/app/config/ssl/certs/localhost+2.pem"]
