import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Chip,
  Stack,
  Tooltip
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  VolumeUp,
  VolumeOff,
  Fullscreen,
  FullscreenExit
} from '@mui/icons-material';
import API from '../../services/api';

const VideoPlayer = ({ video, onViewRecord }) => {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [watchStartTime, setWatchStartTime] = useState(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime);
    };

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration);
    };

    const handlePlay = () => {
      setIsPlaying(true);
      setWatchStartTime(Date.now());
    };

    const handlePause = () => {
      setIsPlaying(false);
      recordWatchTime();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      recordWatchTime();
    };

    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);

    return () => {
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, []);

  const recordWatchTime = async () => {
    if (!watchStartTime || !onViewRecord) return;

    const watchDuration = (Date.now() - watchStartTime) / 1000; // Convert to seconds
    
    try {
      await API.post(`/videos/${video.id}/view`, {
        video_id: video.id,
        watch_duration: watchDuration
      });
      
      if (onViewRecord) {
        onViewRecord(watchDuration);
      }
    } catch (error) {
      console.error('Failed to record view:', error);
    }
  };

  const togglePlay = () => {
    const videoElement = videoRef.current;
    if (isPlaying) {
      videoElement.pause();
    } else {
      videoElement.play();
    }
  };

  const toggleMute = () => {
    const videoElement = videoRef.current;
    videoElement.muted = !videoElement.muted;
    setIsMuted(videoElement.muted);
  };

  const toggleFullscreen = () => {
    const videoElement = videoRef.current;
    
    if (!isFullscreen) {
      if (videoElement.requestFullscreen) {
        videoElement.requestFullscreen();
      } else if (videoElement.webkitRequestFullscreen) {
        videoElement.webkitRequestFullscreen();
      } else if (videoElement.msRequestFullscreen) {
        videoElement.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleSeek = (event) => {
    const videoElement = videoRef.current;
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    videoElement.currentTime = newTime;
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const videoUrl = `${API.defaults.baseURL}${video.file_url}`;

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Video Info */}
      <Paper 
        sx={{ 
          p: 3, 
          mb: 2,
          backgroundColor: '#0f1419',
          border: '2px solid #1a5a7a',
          borderRadius: '1.2rem'
        }}
      >
        <Typography variant="h5" sx={{ color: '#f8fafc', mb: 2 }}>
          {video.title}
        </Typography>
        
        {video.description && (
          <Typography variant="body1" sx={{ color: '#94a3b8', mb: 2 }}>
            {video.description}
          </Typography>
        )}

        <Stack direction="row" spacing={2} flexWrap="wrap" sx={{ mb: 2 }}>
          {video.category && (
            <Chip 
              label={video.category} 
              size="small"
              sx={{ 
                backgroundColor: '#10466b',
                color: '#f8fafc',
                '&:hover': { backgroundColor: '#1a5a7a' }
              }}
            />
          )}
          
          <Chip 
            label={formatFileSize(video.file_size)} 
            size="small"
            variant="outlined"
            sx={{ 
              borderColor: '#1a5a7a',
              color: '#94a3b8'
            }}
          />
          
          {video.duration && (
            <Chip 
              label={formatTime(video.duration)} 
              size="small"
              variant="outlined"
              sx={{ 
                borderColor: '#1a5a7a',
                color: '#94a3b8'
              }}
            />
          )}
          
          {video.format && (
            <Chip 
              label={video.format.toUpperCase()} 
              size="small"
              variant="outlined"
              sx={{ 
                borderColor: '#1a5a7a',
                color: '#94a3b8'
              }}
            />
          )}
        </Stack>

        {video.tags && (
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {video.tags.split(',').map((tag, index) => (
              <Chip 
                key={index}
                label={tag.trim()} 
                size="small"
                variant="outlined"
                sx={{ 
                  borderColor: '#4a9eff44',
                  color: '#4a9eff',
                  fontSize: '0.75rem'
                }}
              />
            ))}
          </Stack>
        )}
      </Paper>

      {/* Video Player */}
      <Paper 
        sx={{ 
          backgroundColor: '#0a0f1a',
          border: '2px solid #1a5a7a',
          borderRadius: '1.2rem',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <Box sx={{ position: 'relative' }}>
          <video
            ref={videoRef}
            width="100%"
            style={{ display: 'block' }}
            poster={video.thumbnail_url}
          >
            <source src={videoUrl} type={`video/${video.format || 'mp4'}`} />
            Your browser does not support the video tag.
          </video>

          {/* Video Controls Overlay */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
              p: 2
            }}
          >
            {/* Progress Bar */}
            <Box
              sx={{
                height: 6,
                backgroundColor: '#131a22',
                borderRadius: 3,
                mb: 2,
                cursor: 'pointer',
                position: 'relative'
              }}
              onClick={handleSeek}
            >
              <Box
                sx={{
                  height: '100%',
                  backgroundColor: '#4a9eff',
                  borderRadius: 3,
                  width: `${duration ? (currentTime / duration) * 100 : 0}%`,
                  transition: 'width 0.1s ease'
                }}
              />
            </Box>

            {/* Control Buttons */}
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Stack direction="row" alignItems="center" spacing={1}>
                <Tooltip title={isPlaying ? 'Pause' : 'Play'}>
                  <IconButton onClick={togglePlay} sx={{ color: '#f8fafc' }}>
                    {isPlaying ? <Pause /> : <PlayArrow />}
                  </IconButton>
                </Tooltip>

                <Tooltip title={isMuted ? 'Unmute' : 'Mute'}>
                  <IconButton onClick={toggleMute} sx={{ color: '#f8fafc' }}>
                    {isMuted ? <VolumeOff /> : <VolumeUp />}
                  </IconButton>
                </Tooltip>

                <Typography variant="body2" sx={{ color: '#f8fafc', ml: 1 }}>
                  {formatTime(currentTime)} / {formatTime(duration)}
                </Typography>
              </Stack>

              <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                <IconButton onClick={toggleFullscreen} sx={{ color: '#f8fafc' }}>
                  {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
                </IconButton>
              </Tooltip>
            </Stack>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default VideoPlayer;
