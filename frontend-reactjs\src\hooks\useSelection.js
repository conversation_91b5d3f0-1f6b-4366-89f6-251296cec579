import { useState } from 'react';
import API from '../services/api';
import { toast } from 'react-hot-toast';

export function useSelection(fetchSuppliers) {
  const [selected, setSelected] = useState({});

  const clearSelection = () => setSelected({});

  const deleteSelected = async () => {
    const ids = Object.keys(selected);
    if (!ids.length) { toast.error('None selected'); return; }
    if (!confirm(`Delete ${ids.length}?`)) return;
    await Promise.all(ids.map(id => API.delete(`/suppliers/${id}`)));
    toast.success('Deleted');
    clearSelection();
    fetchSuppliers();
  };

  return { selected, setSelected, clearSelection, deleteSelected };
}