def clean_str(val):
    """Cleans up input for string fields (removes NaN, None, 'NaT', trims, etc)"""
    if val is None:
        return ""
    sval = str(val).strip()
    if sval.lower() in ("nan", "nat", "none", "null", "nat"):
        return ""
    return sval

def clean_num(val):
    """Cleans up input for numeric fields."""
    if val is None or str(val).lower() in ("nan", "nat", "none", "null", ""):
        return None
    try:
        return float(val)
    except Exception:
        return None

def clean_bool(val):
    if str(val).strip().lower() in ("1", "true", "yes", "y"):
        return True
    if str(val).strip().lower() in ("0", "false", "no", "n", "", "nan", "none", "null"):
        return False
    return False
