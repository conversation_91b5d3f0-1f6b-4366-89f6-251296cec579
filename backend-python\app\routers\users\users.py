from fastapi import APIRouter, Depends, HTTPException, Path, Body, Query, status, Request
from fastapi.responses import JSONResponse, Response
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, delete
from typing import List, Optional
from contextlib import contextmanager
from datetime import datetime, timedelta

from database.database import get_db
from models.users.users import User, UserRole
from schemas.users.users import UserOut, CreateUserRequest, UserUpdateRequest
from routers.auth.auth import get_current_user
from routers.permissions.permissions import require_permission
from auth.auth import verify_password, hash_password

router = APIRouter(prefix="/users", tags=["Users"])

@contextmanager
def atomic_transaction(session: Session):
    try:
        yield
        session.commit()
    except:
        session.rollback()
        raise

def is_recent_login(user, minutes=5):
    """Returns True if user's last_login was within 'minutes' ago."""
    if not user.last_login:
        return False
    return (datetime.utcnow() - user.last_login) < timedelta(minutes=minutes)

# GET /users
@router.get("/", response_model=List[UserOut], dependencies=[Depends(require_permission("Users", "view"))])
async def list_users(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(25, ge=1, le=100),
    search: Optional[str] = Query(None)
):
    query = select(User).options(selectinload(User.roles))
    if search:
        query = query.where(func.lower(User.username).like(f"%{search.lower()}%"))
    query = query.offset((page - 1) * page_size).limit(page_size)
    users = db.exec(query).all()
    return [
        UserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            roles=[ur.role for ur in user.roles],
            last_login=user.last_login.isoformat() if user.last_login else None
        )
        for user in users
    ]

# POST /users
@router.post("/", response_model=UserOut, status_code=201, dependencies=[Depends(require_permission("Users", "create"))])
async def create_user(
    payload: CreateUserRequest,
    db: Session = Depends(get_db)
):
    existing = db.exec(select(User).where(func.lower(User.username) == payload.username.lower())).first()
    if existing:
        raise HTTPException(status_code=409, detail="Username already exists.")
    with atomic_transaction(db):
        user = User(username=payload.username, email=payload.email, password_hash=hash_password(payload.password))
        db.add(user)
        db.flush()
        for role in payload.roles:
            db.add(UserRole(user_id=user.id, role=role))
        db.refresh(user)
    return UserOut(
        id=user.id,
        username=user.username,
        email=user.email,
        roles=[ur.role for ur in user.roles],
        last_login=user.last_login.isoformat() if user.last_login else None
    )

# PUT /users/{user_id}
@router.put("/{user_id}", response_model=UserOut, dependencies=[Depends(require_permission("Users", "update"))])
async def update_user(
    user_id: int = Path(...),
    payload: UserUpdateRequest = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    user = db.exec(select(User).options(selectinload(User.roles)).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    with atomic_transaction(db):
        if payload.password:
            # Only allow password change if recent login
            if not is_recent_login(current_user):
                raise HTTPException(
                    status_code=401,
                    detail="For security reasons, please log in again to change your password."
                )
            # Only self or admin (optional: add logic if only self should be allowed)
            user.password_hash = hash_password(payload.password)
        if payload.roles is not None:
            # Recent login required for role updates (especially for self/admins)
            if not is_recent_login(current_user):
                raise HTTPException(
                    status_code=401,
                    detail="For security reasons, please log in again to update user roles."
                )
            db.exec(delete(UserRole).where(UserRole.user_id == user.id))
            for role in payload.roles:
                db.add(UserRole(user_id=user.id, role=role))
        db.refresh(user)
    return UserOut(
        id=user.id,
        username=user.username,
        roles=[ur.role for ur in user.roles],
        last_login=user.last_login.isoformat() if user.last_login else None
    )

# BULK UPDATE /users/bulk (optional)
@router.put("/bulk", dependencies=[Depends(require_permission("Users", "update"))])
async def bulk_update_roles(
    user_ids: List[int] = Body(..., embed=True),
    roles: List[str] = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    if not is_recent_login(current_user):
        raise HTTPException(
            status_code=401,
            detail="For security reasons, please log in again to update user roles."
        )
    with atomic_transaction(db):
        for uid in user_ids:
            user = db.exec(select(User).where(User.id == uid)).first()
            if user:
                db.exec(delete(UserRole).where(UserRole.user_id == uid))
                for role in roles:
                    db.add(UserRole(user_id=uid, role=role))
    return {"updated_users": user_ids, "new_roles": roles}

# DELETE /users/{user_id}
@router.delete("/{user_id}", status_code=204, dependencies=[Depends(require_permission("Users", "delete"))])
async def delete_user(
    user_id: int = Path(...),
    req: Request = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    body = await req.json()
    password = body.get("password")
    if not password:
        raise HTTPException(status_code=400, detail="Password required to confirm deletion.")
    if current_user.id == user_id:
        raise HTTPException(status_code=403, detail="Admins cannot delete their own account.")

    # Require recent login for sensitive actions
    if not is_recent_login(current_user):
        raise HTTPException(
            status_code=401,
            detail="For security reasons, please log in again to confirm this action."
        )
    user = db.exec(select(User).options(selectinload(User.roles)).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")

    if not verify_password(password, current_user.password_hash):
        raise HTTPException(status_code=401, detail="Password is incorrect. Please try again.")

    # Check if deleting the last admin
    user_roles = [ur.role for ur in user.roles]
    if "admin" in user_roles:
        # Count all admins (excluding the one being deleted)
        total_admins = db.exec(
            select(User).join(UserRole).where(UserRole.role == "admin", User.id != user_id)
        ).count()
        if total_admins == 0:
            raise HTTPException(
                status_code=403,
                detail="You cannot delete the last remaining admin account."
            )
    with atomic_transaction(db):
        db.exec(delete(UserRole).where(UserRole.user_id == user_id))
        db.delete(user)
    return Response(status_code=status.HTTP_204_NO_CONTENT)
