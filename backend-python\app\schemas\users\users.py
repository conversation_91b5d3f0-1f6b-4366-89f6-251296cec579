from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional, Annotated

ALLOWED_ROLES = [
    "admin",
    "manager",
    "supervisor",
    "lead",
    "waiver",
    "replenish",
    "stow",
    "pick",
    "pack",
    "viewer",
    "user"
]

ROLE_HIERARCHY = {
    "admin": 5,
    "manager": 4,
    "supervisor": 3,
    "lead": 2,
    # all other roles are level 0
    "waiver": 1,
    "replenish": 0,
    "stow": 0,
    "pick": 0,
    "pack": 0,
    "viewer": 0,
    "user": 0
}

class UserOut(BaseModel):
    id: int
    username: str
    email: EmailStr
    roles: List[str]
    last_login: Optional[str] = None
    
    model_config = {
        "from_attributes": True
    }

class CreateUserRequest(BaseModel):
    username: str
    email: str
    password: Annotated[str, Field(min_length=6)]
    roles: List[str]

    @validator("roles", each_item=True)
    def validate_role(cls, v):
        if v not in ALLOWED_ROLES:
            raise ValueError(f"Invalid role: {v}")
        return v

class UserUpdateRequest(BaseModel):
    roles: Optional[List[str]] = None
    password: Optional[Annotated[str, Field(min_length=6)]] = None

    @validator("roles", each_item=True)
    def validate_role(cls, v):
        if v not in ALLOWED_ROLES:
            raise ValueError(f"Invalid role: {v}")
        return v