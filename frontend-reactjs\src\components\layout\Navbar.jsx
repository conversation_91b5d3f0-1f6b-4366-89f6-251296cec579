import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import API from "../../services/api";
import { useAuth } from "../../context/auth/AuthProvider";
import { usePermissions } from "../../context/permissions/PermissionsProvider";
import toast from "react-hot-toast";
import { Menu, X } from "lucide-react";
import { Switch, FormControlLabel } from "@mui/material";
import PaginationToggle from "../tables/PaginationToggle";

export default function Navbar() {
  const navigate = useNavigate();
  const { setIsAuthenticated } = useAuth();
  const { perms } = usePermissions();
  const [userInfo, setUserInfo] = useState({ username: "", roles: [] });
  const [mobileOpen, setMobileOpen] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const { data } = await API.get("/auth/validate");
        setUserInfo({ username: data.username, roles: data.roles || [] });
      } catch (err) {
        console.error("Failed to fetch user info", err);
      }
    })();
  }, []);

  const handleLogout = async () => {
    const confirmed = window.confirm("Are you sure you want to log out?");
    if (!confirmed) return;

    try {
      await API.post("/auth/logout");
      setIsAuthenticated(false);
      toast.success("Logged out");
      navigate("/login");
    } catch (err) {
      console.error("Logout failed", err);
      toast.error("Logout failed. Please try again.");
    }
  };

  const canView = (module) =>
    perms.some((p) => p.module === module && p.action === "view" && p.allowed);

  const navLinks = [
    { label: "Landing", path: "/landing" },
    { label: "Home", path: "/home" },
    { label: "Business", path: "/business", condition: canView("Business") },
    { label: "Operations", path: "/operations", condition: canView("Operations") },
    { label: "Settings", path: "/settings", condition: canView("Settings") },
  ].filter((link) => link.condition !== false);

  const navButtonClass =
    "px-4 py-2 text-sm font-medium text-white rounded-lg hover:bg-gray-800 transition min-w-fit";

  return (
    <nav className="sticky top-0 z-50 bg-gray-900 text-white py-3 shadow-md">
      <div className="w-full flex items-center justify-between">

        {/* Left nav buttons */}
        <div className="hidden md:flex items-center gap-6 pl-[15px]">
          {navLinks.map((link) => (
            <button
              key={link.path}
              onClick={() => navigate(link.path)}
              className={navButtonClass}
            >
              {link.label}
            </button>
          ))}
        
        {/* Pagination toggle */}
         <div className="flex items-center">
           <PaginationToggle />
         </div>
        </div>

        {/* <div className="flex items-center justify-start mb-4 px-2">
        <FormControlLabel
          control={
            <Switch
              checked={paginationMode === "server"}
              onChange={(e) =>
                setPaginationMode(e.target.checked ? "server" : "client")
              }
              color="primary"
            />
          }
          label={
            <span className="text-white text-sm">
              {paginationMode === "server" ? "Server-side Pagination" : "Client-side Pagination"}
            </span>
          }
        />
      </div> */}

        {/* Mobile menu button */}
        <div className="md:hidden pl-4">
          <button onClick={() => setMobileOpen(!mobileOpen)}>
            {mobileOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Logout */}
        <div className="flex items-center gap-4 pr-[15px]">
          <button
            onClick={handleLogout}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-500 transition min-w-fit"
          >
            Logout
          </button>
        </div>
      </div>

      {/* Mobile dropdown nav */}
      {mobileOpen && (
        <div className="md:hidden mt-4 flex flex-col gap-3 bg-gray-800 p-4 rounded-lg shadow">
          {navLinks.map((link) => (
            <button
              key={link.path}
              onClick={() => {
                navigate(link.path);
                setMobileOpen(false);
              }}
              className={navButtonClass}
            >
              {link.label}
            </button>
          ))}
        </div>
      )}
    </nav>
  );
}
