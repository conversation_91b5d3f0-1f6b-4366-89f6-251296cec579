from pydantic import BaseModel, validator, Field
from typing import Optional, List
from datetime import datetime


class VideoBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field("", max_length=1000)
    is_public: bool = Field(default=False)
    tags: Optional[str] = Field("", max_length=500)
    category: Optional[str] = Field("", max_length=100)

    @validator('title', 'description', 'tags', 'category', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()


class VideoCreate(VideoBase):
    pass


class VideoUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    is_public: Optional[bool] = None
    tags: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, max_length=100)

    @validator('title', 'description', 'tags', 'category', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        return str(v).strip()


class VideoOut(VideoBase):
    id: int
    filename: str
    original_filename: str
    file_url: str
    file_size: int
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = ""
    codec: Optional[str] = ""
    bitrate: Optional[int] = None
    frame_rate: Optional[float] = None
    thumbnail_url: Optional[str] = ""
    uploaded_by: Optional[int] = None
    uploaded_at: datetime
    updated_at: Optional[datetime] = None
    is_processed: bool = False

    class Config:
        from_attributes = True


class VideoListOut(BaseModel):
    id: int
    title: str
    description: str
    thumbnail_url: Optional[str] = ""
    duration: Optional[float] = None
    file_size: int
    uploaded_at: datetime
    is_public: bool
    category: Optional[str] = ""
    view_count: Optional[int] = 0

    class Config:
        from_attributes = True


class VideoUploadResponse(BaseModel):
    id: int
    title: str
    file_url: str
    message: str = "Video uploaded successfully"


class VideoStreamInfo(BaseModel):
    id: int
    title: str
    file_url: str
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = ""
    file_size: int


class VideoMetadataOut(BaseModel):
    id: int
    video_id: int
    metadata_key: str
    metadata_value: str
    created_at: datetime

    class Config:
        from_attributes = True


class VideoViewCreate(BaseModel):
    video_id: int
    watch_duration: Optional[float] = None


class VideoViewOut(BaseModel):
    id: int
    video_id: int
    viewer_ip: Optional[str] = ""
    user_id: Optional[int] = None
    viewed_at: datetime
    watch_duration: Optional[float] = None

    class Config:
        from_attributes = True


class VideoSearchParams(BaseModel):
    query: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None
    uploaded_by: Optional[int] = None
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    sort_by: str = Field(default="uploaded_at", regex="^(uploaded_at|title|duration|file_size|view_count)$")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$")
