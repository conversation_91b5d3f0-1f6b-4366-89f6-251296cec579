// src/components/business/suppliers/MergeWizard.jsx

import React, { useState, useEffect } from "react";
import {
  Dialog, DialogTitle, DialogContent, DialogActions, Typography,
  Table, TableHead, TableRow, TableCell, TableBody, Button,
  Radio, RadioGroup, FormControlLabel
} from "@mui/material";

const clean_str = (val) => {
  return (
    val === undefined ||
    val === null ||
    val === "" ||
    val === "nan" ||
    val === "NaN" ||
    val === "NaT" ||
    val === "nat" ||
    (typeof val === "number" && isNaN(val))
  );
};

export default function MergeWizard({
  open,
  duplicate,
  existing,
  imported,
  mergeFields,
  mergeChoices,
  setMergeChoices,
  onCancel,
  onSave,
}) {
  const [localChoices, setLocalChoices] = useState(mergeChoices);

  useEffect(() => {
    if (open) setLocalChoices(mergeChoices);
  }, [mergeChoices, open, duplicate, existing]);

  const getValue = (obj, field, isImported = false) => {
    if (!obj) return "";
  
    if (isImported) {
      // Imported is always flat: just lookup field.key directly
      let val = obj[field.key];
      if (val && typeof val === "object" && "value" in val) val = val.value;
      return clean_str(val) ? "" : val;
    }
  
    // For existing: use nested logic (profile, contacts, addresses)
    if (field.nested === "profile") {
      return obj.profile?.[field.key] ?? "";
    }
    if (field.nested === "contacts") {
      const contact =
        Array.isArray(obj.contacts) &&
        (obj.contacts.find((c) => c.is_primary) || obj.contacts[0]);
      if (!contact) return "";
      switch (field.key) {
        case "primary_contact_name":
          return contact.name ?? "";
        case "primary_contact_email":
          return contact.email ?? "";
        case "primary_contact_phone":
          return contact.phone ?? "";
        case "primary_contact_title":
          return contact.title ?? "";
        default:
          return "";
      }
    }
    if (field.nested === "addresses") {
      const type = field.type;
      const address =
        Array.isArray(obj.addresses) &&
        obj.addresses.find((a) => a.type === type);
      if (!address) return "";
      const fieldMap = {
        billing_street_address: "street_address",
        billing_city: "city",
        billing_state: "state",
        billing_zipcode: "zipcode",
        billing_phone: "phone",
        billing_contact_email: "contact_email",
        billing_notes: "notes",
        shipping_street_address: "street_address",
        shipping_city: "city",
        shipping_state: "state",
        shipping_zipcode: "zipcode",
        shipping_phone: "phone",
        shipping_contact_email: "contact_email",
        shipping_notes: "notes",
      };
      return address[fieldMap[field.key]] ?? "";
    }
  
    // Fallback
    let val = obj[field.key];
    if (val && typeof val === "object" && "value" in val) val = val.value;
    return clean_str(val) ? "" : val;
  };
  

  const display = (val, field) =>
    field.key === "credit_limit" && !clean_str(val) && !isNaN(val)
      ? (parseFloat(val) % 1 === 0
          ? parseInt(val, 10).toLocaleString()
          : val)
      : (!clean_str(val) ? val : <i>(empty)</i>);

  const handleSave = () => {
    setMergeChoices(localChoices);
    if (onSave) onSave();
  };

  return (
    <Dialog open={open} maxWidth="md" fullWidth>
      <DialogTitle
        sx={{
          bgcolor: "#181f36",
          color: "#a5b4fc",
          borderBottom: "1px solid #334155",
          fontWeight: 700,
          fontSize: "1.25rem",
          letterSpacing: 1,
          pb: 2,
          px: 4,
        }}
      >
        Merge Supplier: {duplicate?.company_name} / {duplicate?.company_code}
      </DialogTitle>
      <DialogContent sx={{ px: 4, pt: 2, pb: 0 }}>
        <Typography sx={{ color: "#fb7185", mb: 2 }}>
          Select which value to keep for each field.<br />
          <span style={{ color: "#f59e42", fontWeight: 600 }}>
            Warning: <span style={{ color: "#fb7185" }}>*</span> Overwriting with an empty imported field will erase the existing value!
          </span>
        </Typography>
        <Table size="small" sx={{ bgcolor: "#1e293b", borderRadius: 2, border: "1.5px solid #334155", mb: 2 }}>
          <TableHead>
            <TableRow sx={{ bgcolor: "#232949" }}>
              <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Field</TableCell>
              <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Existing Value</TableCell>
              <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Imported Value</TableCell>
              <TableCell sx={{ color: "#a5b4fc", fontWeight: 700 }}>Keep</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {mergeFields.map((field) => {
              const existingVal = getValue(existing, field, false);
              const importedVal = getValue(imported, field, true);
              const importedIsEmpty = importedVal === "";
              const showWarning = localChoices[field.key] === "imported" && importedIsEmpty;
              const isReadOnlyField = field.key === "company_name" || field.key === "company_code";

              return (
                <TableRow key={field.key}>
                  <TableCell>{field.label}</TableCell>
                  <TableCell sx={{ color: "#94a3b8" }}>
                    {display(existingVal, field)}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: importedIsEmpty ? "#fb7185" : "#4ade80",
                      fontWeight: importedIsEmpty ? 600 : undefined,
                    }}
                  >
                    {display(importedVal, field)}
                    {showWarning && (
                      <span
                        style={{
                          color: "#fb7185",
                          fontWeight: 700,
                          marginLeft: 8,
                          fontSize: "0.95em",
                        }}
                      >
                        * Will erase value!
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    <RadioGroup
                      row
                      value={isReadOnlyField ? "existing" : (localChoices[field.key] || "existing")}
                      onChange={(e) =>
                        setLocalChoices((prev) => ({
                          ...prev,
                          [field.key]: e.target.value,
                        }))
                      }
                    >
                      <FormControlLabel value="existing" control={<Radio />} label="Existing" disabled={isReadOnlyField}/>
                      <FormControlLabel value="imported" control={<Radio />} label="Imported" disabled={isReadOnlyField}/>
                    </RadioGroup>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </DialogContent>
      <DialogActions sx={{ bgcolor: "#181f36", borderTop: "1px solid #334155", py: 2, px: 4, gap: 2 }}>
        <Button
          variant="outlined"
          onClick={() => {
            setLocalChoices((prev) => {
              const updated = { ...prev };
              mergeFields.forEach((field) => {
                if (field.key !== "company_name" && field.key !== "company_code") {
                  updated[field.key] = "existing";
                }
              });
              return updated;
            });
          }}
          sx={{
            color: "#e0e7ff",
            borderColor: "#6366f1",
            "&:hover": { bgcolor: "#334155" },
          }}
        >
          Select Existing All
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            setLocalChoices((prev) => {
              const updated = { ...prev };
              mergeFields.forEach((field) => {
                if (field.key !== "company_name" && field.key !== "company_code") {
                  updated[field.key] = "imported";
                }
              });
              return updated;
            });
          }}
          sx={{
            color: "#e0e7ff",
            borderColor: "#6366f1",
            "&:hover": { bgcolor: "#334155" },
          }}
        >
          Select Imported All
        </Button>
        <Button
          onClick={onCancel}
          sx={{
            bgcolor: "#334155",
            color: "#e0e7ff",
            borderRadius: 2,
            fontWeight: 600,
            "&:hover": { bgcolor: "#475569" },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            bgcolor: "#6366f1",
            color: "#e0e7ff",
            fontWeight: 600,
            borderRadius: 2,
            boxShadow: "0 0 8px 0 #6366f1bb",
            "&:hover": { bgcolor: "#818cf8" },
          }}
        >
          Save Merge Choices
        </Button>
      </DialogActions>
    </Dialog>
  );
}
