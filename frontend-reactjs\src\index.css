@tailwind base;
@tailwind components;
@tailwind utilities;

/* Smooth scrolling and full-height layout */
html, body, #root {
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
  overflow: hidden;
  background: #fff;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #1e293b #0f172a;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  background: #0f172a;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #1e293b;
  border-radius: 6px;
  box-shadow: 0 0 2px #181f36;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: #0f172a;
  border-radius: 6px;
}
