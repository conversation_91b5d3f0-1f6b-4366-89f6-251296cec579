// SupplierTable.styles.js

// ─── Color Palette ─────────────────────────────────────────────────────────────
export const COSMIC_BG         = '#111827'; // Deep cosmic void
export const HEADER_BG         = '#111827'; // Nebula gradient base
export const HEADER_GRADIENT   = '#111827';
export const ROW_EVEN          = '#111827'; // Even row nebula
export const ROW_ODD           = '#111827'; // Odd row void
export const STICKY_BG         = '#111827'; // Pinned cell background
export const BORDER_COLOR      = '#2a2f4a'; // Subtle dark divider
export const ACCENT_PRIMARY    = '#111827af0'; // Neon purple
export const ACCENT_SECONDARY  = '#111827'; // Cyan accent

// ─── Table Styling Object ────────────────────────────────────────────────────
export const tableStyling = {
  // Surrounding Paper
  muiTablePaperProps: {
    sx: {
      backgroundColor: COSMIC_BG,
      boxShadow: 'none',
      borderRadius: 0,
      color: ACCENT_SECONDARY,
    },
  },

  // Container (scrollbars, bg)
  muiTableContainerProps: {
    sx: {
      backgroundColor: COSMIC_BG,
      // If you want the custom scrollbar only here, enable:
      // scrollbarWidth: 'thin',
      // '::-webkit-scrollbar': { width: 8 },
      // '::-webkit-scrollbar-thumb': {
      //   backgroundColor: ROW_EVEN,
      //   borderRadius: 8,
      //   boxShadow: `0 0 8px ${ACCENT_PRIMARY}, 0 0 2px ${ROW_ODD}`,
      // },
      // '::-webkit-scrollbar-track': { background: ROW_ODD },
    },
  },

  // Table Head
  muiTableHeadProps: {
    sx: {
      background: HEADER_GRADIENT,
    },
  },
  muiTableHeadCellProps: {
    sx: {
      color: ACCENT_SECONDARY,
      borderBottom: `1px solid ${BORDER_COLOR}`,
      textTransform: 'uppercase',
      fontSize: '0.875rem',
      fontWeight: 600,
    },
  },

  // Table Body
  muiTableBodyProps: {
    sx: {
      // (you can add row-level sx here if desired)
    },
  },
  muiTableBodyCellProps: ({ cell }) => {
    const isPinned = cell.getIsPinned();
    const bgColor = isPinned
      ? HEADER_BG
      : cell.row.index % 2 === 0
        ? ROW_EVEN
        : ROW_ODD;

    return {
      sx: {
        backgroundColor: bgColor,
        color: '#ffffff',
        borderBottom: `1px solid ${BORDER_COLOR}`,

        // Pinned-column divider
        ...(isPinned && {
          boxShadow: 'none',
          borderRight: `1px solid ${BORDER_COLOR}`,
          zIndex: 3,
        }),

        // Hover glow on individual cells
        '&:hover': {
          boxShadow: `0 0 8px ${ACCENT_SECONDARY}`,
        },
      },
    };
  },
};
