services:
  db:
    image: postgres:17
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
      POSTGRES_DB: db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  backend:
    build: 
      context: ./backend-python
    container_name: backend-python
    env_file:
      - ./backend-python/.env
    ports:
      - "8000:8000"
    working_dir: /app
    volumes:
      - ./backend-python/app:/app
    restart: unless-stopped
    depends_on:
      - db

  frontend:
    build:
      context: ./frontend-reactjs
    image: frontend-reactjs
    container_name: frontend-reactjs
    env_file:
      - ./frontend-reactjs/.env
    ports:
      - "3000:443"
      - "3001:80"
    volumes:
      - ./frontend-reactjs:/app
      - /app/node_modules
    command: npm start
    depends_on:
      - backend

  migrate:
    image: migrate/migrate
    container_name: migrate
    entrypoint: ["/migration.sh", "--host", "db", "--port", "5432", "--timeout", "30", "--"]
    command:
      [
        "migrate",
        "-path", "/migrations",
        "-database", "*********************************/db?sslmode=disable",
        "up"
      ]
    volumes:
      - ./backend-python/app/database/migrations:/migrations
      - ./backend-python/app/database/migrations/migration.sh:/migration.sh 
    depends_on:
      - db
    restart: on-failure

volumes:
  pgdata:

networks:
  default:
    driver: bridge
