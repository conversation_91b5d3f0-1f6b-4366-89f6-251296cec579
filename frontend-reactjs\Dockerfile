# 1. Build the React app
FROM node:24.1.0-slim AS build

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# 2. Serve the production build with Nginx and SSL
FROM nginx:1.25-alpine

RUN rm -rf /usr/share/nginx/html/*
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom nginx config (make sure to create this!)
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Copy SSL certificates (make sure these paths match your local repo!)
COPY config/ssl/certs/localhost+2.pem /etc/nginx/ssl/localhost+2.pem
COPY config/ssl/certs/localhost+2-key.pem /etc/nginx/ssl/localhost+2-key.pem

EXPOSE 443
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
