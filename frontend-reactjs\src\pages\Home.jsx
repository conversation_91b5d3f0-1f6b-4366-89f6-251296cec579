import React from "react";
import { useNavigate } from "react-router-dom";

export default function Landing() {
  const navigate = useNavigate();

  const cards = [
    {
      key: "landing",
      title: "Landing",
      desc: "Return to the landing page.",
      route: "/landing",
    },
    {
      key: "business",
      title: "Business",
      desc: "Access business workflows and reports.",
      route: "/business",
    },
    {
      key: "operations",
      title: "Operations",
      desc: "Manage warehouse operations and tasks.",
      route: "/operations",
    },
    {
      key: "settings",
      title: "Settings",
      desc: "View and edit system settings.",
      route: "/settings",
    },
  ];

  const handleLogout = () => navigate("/login");

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white">
      {/* <Navbar handleLogout={handleLogout} /> */}

      <div className="max-w-7xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-semibold mb-10 text-white text-center">
          Home
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
          {cards.map(({ key, title, desc, route }) => (
            <div
              key={key}
              onClick={() => navigate(route)}
              className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex flex-col justify-start h-full min-h-[220px] transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-purple-500"
            >
              <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
              <p className="text-gray-400 text-sm">{desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
