// CreateNewSupplierModal.jsx
import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import toast from 'react-hot-toast';

import API from '../../../services/api';
import SupplierDetailPanel from './SupplierDetailPanel';

/**
 * Wraps SupplierDetailPanel in a Dialog, using a maxWidth that matches
 * the table container to align borders, rather than full-screen width.
 */
export default function CreateNewSupplierModal({ open, onClose, onSuccess, supplier }) {
  const [payload, setPayload] = useState(supplier || {});

  useEffect(() => {
    setPayload(supplier || {});
  }, [supplier]);

  const handleSave = async () => {
    try {
      const url = supplier?.id ? `/suppliers/${supplier.id}` : '/suppliers';
      const method = supplier?.id ? API.put : API.post;
      const response = await method(url, payload);
      toast.success(`Supplier ${supplier?.id ? 'updated' : 'created'}`);
      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error(error);
      toast.error('Save failed');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      scroll="paper"
      PaperProps={{
        sx: {
      width: '1400px',          
      minWidth: '900px',
      maxWidth: '95vw',
      height: '660px',         
      minHeight: '700px',      
      maxHeight: '95vh',        // ensures it doesn't go off the screen
      background: '#101826',
      borderRadius: 3,
      boxShadow: '0 8px 48px #000c',
      display: 'flex',
      flexDirection: 'column',
    },
  }}
>
      {/* <DialogTitle sx={{ borderBottom: '1px solid #282b44', background: '#18202e', color: '#e0e7ff' }}>
        {supplier?.id ? 'Edit Supplier' : 'Add New Supplier'}
      </DialogTitle> */}
      <DialogContent dividers sx={{ p: 0, background: '#101826' }}>
        <SupplierDetailPanel
          supplier={payload}
          onChange={setPayload}
          onClose={onClose}
        />
      </DialogContent>
      {/* <DialogActions sx={{ background: '#18202e', borderTop: '1px solid #282b44', p: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained">
          {supplier?.id ? 'Update Supplier' : 'Save Supplier'}
        </Button>
      </DialogActions> */}
    </Dialog>
  );
}
