from fastapi import APIRouter, HTTPException, Depends, Response, <PERSON>ie, Body
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from typing import List
from datetime import datetime

from database.database import get_db
from auth.auth import verify_password, create_access_token, decode_access_token
from utilities.logs.config import log_event
from models.users.users import User, UserRole
from models.models import Module, RolePermission
from schemas.auth.auth import (
    LoginRequest,
    ModuleOut,
    PermissionOut,
    PermissionUpdate,
)

router = APIRouter(prefix="/auth", tags=["Authentication"])

from jose import JWTError, ExpiredSignatureError

def get_current_user(
    access_token: str = <PERSON><PERSON>(default=None),
    session: Session = Depends(get_db),
) -> User:
    if not access_token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    try:
        payload = decode_access_token(access_token)
    except ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

    email = payload.get("sub")
    if not email:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = session.exec(
        select(User)
        .where(User.email == email)
        .options(selectinload(User.roles))
    ).first()
    if not user:
        raise HTTPException(status_code=403, detail="User not found")
    return user


@router.post("/login")
def login(
    response: Response,
    session: Session = Depends(get_db),
    login_data: LoginRequest = Body(...),
):
    user = session.exec(
        select(User)
        .where((User.email == login_data.identifier) | (User.username == login_data.identifier))
        .options(selectinload(User.roles))
    ).first()

    if not user or not verify_password(login_data.password, user.password_hash):
        raise HTTPException(status_code=401, detail="Invalid credentials")

    user.last_login = datetime.utcnow()
    session.add(user)
    session.commit()

    # Use username or email as the token subject (sub)
    token = create_access_token(data={"sub": user.email})  # or user.email

    response.set_cookie(
        key="access_token",
        value=token,
        httponly=True,
        secure=False,
        samesite="lax",
        max_age=1800,
        path="/",
    )

    roles = [ur.role for ur in user.roles]
    return {
        "message": "Authentication successful",
        "roles": roles,
        "last_login": user.last_login.isoformat()
    }


@router.get("/validate")
def validate_current_user(user: User = Depends(get_current_user)):
    return {
        "username": user.username,
        "email": user.email,
        "roles": [r.role for r in user.roles],
        "last_login": user.last_login.isoformat() if user.last_login else None,
    }


@router.post("/logout")
def logout(response: Response):
    response.delete_cookie("access_token")
    return {"message": "Logged out successfully"}


@router.post("/refresh")
def refresh_token(
    response: Response,
    access_token: str = Cookie(default=None),
    session: Session = Depends(get_db),
):
    if not access_token:
        raise HTTPException(status_code=401, detail="No token provided")
    payload = decode_access_token(access_token)
    email = payload.get("sub")
    if not email:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = session.exec(
        select(User)
        .where(User.email == email)
        .options(selectinload(User.roles))
    ).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    new_token = create_access_token(data={"sub": email})
    response.set_cookie(
        key="access_token",
        value=new_token,
        httponly=True,
        secure=False,
        samesite="lax",
        max_age=1800,
        path="/",
    )
    return {"message": "Token refreshed"}
