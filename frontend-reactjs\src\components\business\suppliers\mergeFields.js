// src/components/business/suppliers/mergeFields.js
const mergeFields = [
    { key: "company_name", label: "Company Name" },
    { key: "company_code", label: "Company Code" },
    { key: "notes", label: "Notes" },
    { key: "billing_street_address", label: "Billing Street", nested: "addresses", type: "billing" },
    { key: "billing_city", label: "Billing City", nested: "addresses", type: "billing" },
    { key: "billing_state", label: "Billing State", nested: "addresses", type: "billing" },
    { key: "billing_zipcode", label: "Billing Zip", nested: "addresses", type: "billing" },
    { key: "billing_phone", label: "Billing Phone", nested: "addresses", type: "billing" },
    { key: "billing_contact_email", label: "Billing Contact Email", nested: "addresses", type: "billing" },
    { key: "shipping_street_address", label: "Shipping Street", nested: "addresses", type: "shipping" },
    { key: "shipping_city", label: "Shipping City", nested: "addresses", type: "shipping" },
    { key: "shipping_state", label: "Shipping State", nested: "addresses", type: "shipping" },
    { key: "shipping_zipcode", label: "Shipping Zip", nested: "addresses", type: "shipping" },
    { key: "shipping_contact_email", label: "Shipping Contact Email", nested: "addresses", type: "shipping" },
    { key: "shipping_phone", label: "Shipping Contact Phone", nested: "addresses", type: "shipping" },
    { key: "primary_contact_name", label: "Primary Contact Name", nested: "contacts" },
    { key: "primary_contact_email", label: "Primary Contact Email", nested: "contacts" },
    { key: "primary_contact_phone", label: "Primary Contact Phone", nested: "contacts" },
    { key: "primary_contact_title", label: "Primary Contact Title", nested: "contacts" },
    { key: "payment_terms", label: "Payment Terms", nested: "profile" },
    { key: "credit_limit", label: "Credit Limit", nested: "profile" },
    { key: "credit_hold", label: "Credit Hold", nested: "profile" },
    { key: "sla_description", label: "SLA Description", nested: "profile" },
    // { key: "contract_url", label: "Contract URL", nested: "profile" },
    { key: "effective_date", label: "Effective Date", nested: "profile" },
    { key: "expiration_date", label: "Expiration Date", nested: "profile" },
  ];
  
  export default mergeFields;
  