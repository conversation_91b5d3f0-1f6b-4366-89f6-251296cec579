import React from "react";
import { useNavigate } from "react-router-dom";

function Forbidden() {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100 p-6">
      <h1 className="text-5xl font-bold text-red-600 mb-4">403</h1>
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Access Denied</h2>
      <p className="text-gray-600 mb-8 text-center">
        You do not have permission to view this page.
      </p>
      <div className="flex space-x-4">
        <button
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          onClick={() => navigate(-1)}
        >
          Go Back
        </button>
        <button
          className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition"
          onClick={() => navigate("/login")}
        >
          Login Again
        </button>
      </div>
    </div>
  );
}

export default Forbidden;
