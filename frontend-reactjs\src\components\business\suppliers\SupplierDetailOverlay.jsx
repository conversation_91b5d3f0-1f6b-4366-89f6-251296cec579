import React from "react";
import { Box } from "@mui/material";
import SupplierDetailPanel from "./SupplierDetailPanel";

export default function SupplierDetailOverlay({ rect, supplier, onClose, onSuccess }) {
  if (!rect) return null;
  return (
    <Box
      sx={{
        position: "fixed",
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
        bgcolor: "#18202e",
        boxShadow: 24,
        borderRadius: 2,
        p: 3,
        zIndex: 1401,
        overflowY: "auto",
        display: "flex",
        flexDirection: "column",
        border: "2px solid #334155",
      }}
      onClick={e => e.stopPropagation()}
    >
      {supplier && (
        <SupplierDetailPanel
          supplier={supplier}
          onClose={onClose}
          onSuccess={onSuccess}
        />
      )}
    </Box>
  );
}