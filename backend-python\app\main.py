from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from database.database import engine, create_db_and_tables
from utilities.seeds.seeds import seed
from utilities.uploads.upload_paths import UPLOADS_BASE
from routers.auth.auth import router as auth_router
from routers.roles.roles import router as roles_router
from routers.permissions.permissions import router as permissions_router
from routers.users.users import router as users_router
from routers.settings.settings import router as settings_router
from routers.business.business import router as business_router
from routers.business.suppliers.suppliers import router as suppliers_router
from routers.videos.videos import router as videos_router

import os

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://localhost:3000", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/uploads", StaticFiles(directory=UPLOADS_BASE), name="uploads")

from sqlmodel import text
from database.database import engine

app.include_router(auth_router)
app.include_router(roles_router)
app.include_router(permissions_router)
app.include_router(users_router)
app.include_router(settings_router)
app.include_router(business_router)
app.include_router(suppliers_router)
app.include_router(videos_router)

@app.on_event("startup")
def on_startup():
    create_db_and_tables()
    seed()

@app.get("/")
def read_root():
    return {"message": "Application API running successfully!"}

