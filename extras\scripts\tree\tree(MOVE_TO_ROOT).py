import os

EXCLUDE = {'node_modules', 'dist', '.git'}

for root, dirs, files in os.walk('.', topdown=True):
    # Modify dirs in-place to skip excluded directories
    dirs[:] = [d for d in dirs if d not in EXCLUDE]
    level = root.count(os.sep)
    indent = ' ' * 4 * level
    print(f"{indent}{os.path.basename(root)}/")
    if level == 1:
        for f in files:
            print(f"{indent}    {f}")
