#!/bin/sh
#   Use this script to test if a given TCP host/port are available

set -e

TIMEOUT=15
QUIET=0
HOST=""
PORT=""

while [[ $# -gt 0 ]]
do
    key="$1"
    case $key in
        -h|--host)
        HOST="$2"
        shift
        shift
        ;;
        -p|--port)
        PORT="$2"
        shift
        shift
        ;;
        -t|--timeout)
        TIMEOUT="$2"
        shift
        shift
        ;;
        -q|--quiet)
        QUIET=1
        shift
        ;;
        --)
        shift
        break
        ;;
        *)
        shift
        ;;
    esac
done

if [[ "$HOST" = "" || "$PORT" = "" ]]; then
    echo "Usage: $0 --host <host> --port <port> [--timeout <seconds>] [--quiet]"
    exit 1
fi

wait_for() {
    for i in $(seq $TIMEOUT); do
        nc -z "$HOST" "$PORT" >/dev/null 2>&1 && return 0
        sleep 1
    done
    return 1
}

if [[ $QUIET -ne 1 ]]; then
    echo "Waiting for $HOST:$PORT for up to $TIMEOUT seconds..."
fi

if wait_for; then
    if [[ $QUIET -ne 1 ]]; then
        echo "$HOST:$PORT is available."
    fi
    exit 0
else
    if [[ $QUIET -ne 1 ]]; then
        echo "Timeout while waiting for $HOST:$PORT."
    fi
    exit 1
fi
