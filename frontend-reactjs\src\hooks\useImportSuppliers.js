// src/hooks/useImportSuppliers.js
import { useState } from "react";
import toast from "react-hot-toast";
import API from "../services/api";
import * as XLSX from "xlsx";

export function useImportSuppliers({ suppliers, fetchSuppliers }) {
  const [preview, setPreview] = useState(null);
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState(null);

  // This function is called after the backend returns the preview array
  const processPreview = async (previewData, file) => {
    const duplicates = previewData.filter(row => row.duplicates);

    if (duplicates.length === 0) {
      // No duplicates, auto-import all!
      try {
        await handleCommitImport(
          previewData.map(row => ({
            ...row, // retain all fields, or just use { company_name, company_code, action: 'import', ... }
            action: "import",
          }))
        );
        toast.success("All suppliers imported (no duplicates found).");
        setPreview(null);
        setImportFile(null);
        setDuplicateDialogOpen(false);
        fetchSuppliers && fetchSuppliers();
      } catch (err) {
        toast.error("Auto-import failed: " + (err?.response?.data?.detail || err.message));
      }
    } else {
      // Duplicates found, open dialog for user to resolve
      setPreview(previewData);
      setImportFile(file);
      setDuplicateDialogOpen(true);
    }
  };

  // Main import handler (called on file upload)
  const handleImport = async (eventOrFile) => {
    let file;
    if (eventOrFile?.target?.files) {
      file = eventOrFile.target.files[0];
    } else {
      file = eventOrFile;
    }
    if (!file) return;

    const allowedExtensions = [".csv", ".xlsx"];
    const allowedTypes = [
      "text/csv",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel"
    ];
    const fileName = file.name.toLowerCase();
    const fileExt = allowedExtensions.some(ext => fileName.endsWith(ext));
    if (
      (file.type && !allowedTypes.includes(file.type)) &&
      !fileExt
    ) {
      toast.error("Please select a CSV or XLSX file.");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("file", file);
      const res = await API.post("/suppliers/import/preview", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      await processPreview(res.data?.preview || [], file);
    } catch (err) {
      toast.error(
        "Import failed: " +
          (err?.response?.data?.detail || err.message || "Unknown error")
      );
    }
  };

  // Commit import (called by "Import All" or "Apply" in DuplicateDialog)
  const handleCommitImport = async (actions = null) => {
    try {
      const res = await API.post(
        "/suppliers/import/commit",
        actions || preview,
        { headers: { "Content-Type": "application/json" } }
      );
      toast.success("Suppliers imported successfully!");
      setDuplicateDialogOpen(false);
      setPreview(null);
      setImportFile(null);
      fetchSuppliers && fetchSuppliers();
    } catch (err) {
      toast.error("Import commit failed: " + (err?.response?.data?.detail || err.message));
    }
  };

  const handleExport = () => {
    const ws = XLSX.utils.json_to_sheet(suppliers);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Suppliers");
    XLSX.writeFile(wb, "suppliers_export.xlsx");
  };

  const closeDuplicateDialog = () => {
    setDuplicateDialogOpen(false);
    setPreview(null);
    setImportFile(null);
  };

  // You can also pass setPreview, setDuplicateDialogOpen, setImportFile for merge/overwrite logic in dialogs
  return {
    handleImport,
    handleCommitImport,
    handleExport,
    preview,
    duplicateDialogOpen,
    closeDuplicateDialog,
    importFile,
    setPreview,
    setDuplicateDialogOpen,
    setImportFile,
  };
}
