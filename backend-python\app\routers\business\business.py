from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database.database import get_db
from models.users.users import User
from routers.auth.auth import get_current_user 
from routers.permissions.permissions import has_permission

router = APIRouter(prefix="/business", tags=["Business"])

@router.get("/")
def get_business_data(
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not has_permission(user, module_name="Business", action="view", db=db):
        raise HTTPException(status_code=403, detail="Not authorized")
    return {"message": ""}
