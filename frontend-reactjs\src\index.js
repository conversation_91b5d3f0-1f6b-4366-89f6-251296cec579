import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AuthProvider } from "./context/auth/AuthProvider";
import { PermissionsProvider } from './context/permissions/PermissionsProvider';
import './index.css';
import { PaginationModeProvider } from './context/pagination/PaginationProvider';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <PaginationModeProvider>
        <AuthProvider>
          <PermissionsProvider>
            <App />
          </PermissionsProvider>
        </AuthProvider>
      </PaginationModeProvider>
    </BrowserRouter>
  </React.StrictMode>
);