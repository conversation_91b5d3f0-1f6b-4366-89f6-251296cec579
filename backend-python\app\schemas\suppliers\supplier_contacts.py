from pydantic import BaseModel, validator
from typing import Optional

class SupplierContactBase(BaseModel):
    name: Optional[str] = ""
    title: Optional[str] = ""
    phone: Optional[str] = ""
    email: Optional[str] = ""
    is_primary: bool = False

    @validator('name', 'title', 'phone', 'email', pre=True, always=True)
    def clean_str(cls, v):
        if v is None or str(v).strip().lower() in {"nan", "nat", "none", "null"}:
            return ""
        val = str(v).strip()
        # Remove trailing ".0" from phone if present (common Excel/Pandas issue)
        if cls.__name__ == "SupplierContactBase" and val.endswith('.0'):
            val = val[:-2]
        return val

class SupplierContactRead(SupplierContactBase):
    id: int

    class Config:
        from_attributes = True
