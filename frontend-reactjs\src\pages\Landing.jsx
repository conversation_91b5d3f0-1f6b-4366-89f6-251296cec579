import React, { useEffect, useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "../context/auth/AuthProvider";
import { usePermissions } from "../context/permissions/PermissionsProvider";
import API from "../services/api";
import toast from "react-hot-toast";
import testBg from "../images/test.jpg";

export default function Home() {
  const navigate = useNavigate();
  const { setIsAuthenticated } = useAuth();
  const { perms, loaded: permsLoaded } = usePermissions();

  const [userInfo, setUserInfo] = useState({
    username: "",
    email: "",
    roles: [],
    lastLogin: "",
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    API.get("/auth/validate")
      .then((res) => {
        const { username, roles, last_login } = res.data;
        setUserInfo({ username, roles, lastLogin: last_login });
      })
      .catch((err) => {
        console.error("Failed to fetch user info", err);
        toast.error("Authentication error. Please log in again.");
        setIsAuthenticated(false);
        navigate("/login");
      })
      .finally(() => setLoading(false));
  }, [navigate, setIsAuthenticated]);

  const handleLogout = async () => {
    const confirmed = window.confirm("Are you sure you want to log out?");
    if (!confirmed) return;
  
    try {
      await API.post("/auth/logout");
      setIsAuthenticated(false);
      toast.success("Logged out");
      navigate("/login");
    } catch (err) {
      console.error("Logout failed", err);
      toast.error("Logout failed. Please try again.");
    }
  };

  const canView = (moduleName) =>
    perms.some((p) => p.module === moduleName && p.action === "view" && p.allowed);

  if (loading || !permsLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-teal-300 text-lg animate-pulse">Loading...</div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-cover bg-center relative"
      style={{ backgroundImage: `url(${testBg})` }}
    >
      <div className="absolute inset-0 bg-black/65"></div>

      <div className="relative z-10 flex flex-col items-center w-2/6 justify-center min-h-screen p-6">
        <div className="backdrop-blur-sm bg-gray-800 bg-opacity-60 shadow-2xl rounded-2xl p-10 max-w-md text-center">
          <h1 className="text-3xl font-bold text-teal-300 mb-2">
            Welcome back, {userInfo?.username || "Guest"}!
          </h1>
          <p className="text-sm text-gray-400 mb-6">
            Role:{" "}
            <span className="text-teal-200">
              {userInfo.roles.length ? userInfo.roles.join(", ") : "N/A"}
            </span>{" "}
            • Last login:{" "}
            <span className="text-teal-200">
              {userInfo.lastLogin
                ? new Date(userInfo.lastLogin).toLocaleString()
                : "N/A"}
            </span>
          </p>

          <div className="mt-8 grid grid-cols-1 gap-6">
              <Link
                to="/home"
                className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex items-center justify-center h-24 transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-white"
              >
                <span className="text-lg font-semibold text-white">
                  Home
                </span>
              </Link>

            {canView("Operations") && (
              <Link
                to="/operations"
                className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex items-center justify-center h-24 transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-white"
              >
                <span className="text-lg font-semibold text-white">
                  Operations
                </span>
              </Link>
            )}

            {canView("Business") && (
              <Link
                to="/business"
                className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex items-center justify-center h-24 transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-white"
              >
                <span className="text-lg font-semibold text-white">
                  Business Logic
                </span>
              </Link>
            )}

            {canView("Settings") && (
              <Link
                to="/settings"
                className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex items-center justify-center h-24 transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-white"
              >
                <span className="text-lg font-semibold text-white">
                  Settings
                </span>
              </Link>
            )}

            <div className="h-2" />

            <div
              onClick={handleLogout}
              className="bg-red-600 border border-gray-700 rounded-xl shadow-lg p-6 flex items-center justify-center h-24 transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-red-500 hover:border-red-500"
            >
              <span className="text-lg font-semibold text-white">
                Logout
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
