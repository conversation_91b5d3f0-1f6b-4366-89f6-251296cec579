import React, { useState } from "react";
import {
  Box, Dialog, DialogTitle, DialogContent, DialogActions, Button,
  MenuItem, Select, FormControl, InputLabel, TextField, Typography, Checkbox, FormControlLabel, Stack
} from "@mui/material";

const FIELD_OPTIONS = [
  { key: "profile.payment_terms", label: "Payment Terms", type: "select", options: ["Net 15", "Net 30", "Net 45", "Net 60"] },
  { key: "profile.credit_limit", label: "Credit Limit", type: "number" },
  { key: "profile.credit_hold", label: "Credit Hold", type: "checkbox" },
  // { key: "company_code", label: "Company Code", type: "text" },
  // Add more fields as needed
];

export default function MassUpdateModal({ open, onClose, onApply, selectedCount }) {
  const [selectedField, setSelectedField] = useState("");
  const [fieldValue, setFieldValue] = useState("");
  const [batchFields, setBatchFields] = useState([]);

  // Reset on open/close
  React.useEffect(() => {
    if (!open) {
      setSelectedField("");
      setFieldValue("");
      setBatchFields([]);
    }
  }, [open]);

  const handleAddField = () => {
    if (!selectedField) return;
    setBatchFields(fields => [
      ...fields,
      {
        key: selectedField,
        value: fieldValue,
        label: FIELD_OPTIONS.find(f => f.key === selectedField)?.label || selectedField,
      }
    ]);
    setSelectedField("");
    setFieldValue("");
  };

  const handleRemoveField = idx => {
    setBatchFields(fields => fields.filter((_, i) => i !== idx));
  };

  const handleApply = () => {
    if (!batchFields.length) return;
    // Convert to {field: value, ...}
    const updateData = {};
    batchFields.forEach(f => {
      updateData[f.key] = f.value;
    });
    onApply(updateData);
  };

  const renderFieldInput = (fieldObj) => {
    if (!fieldObj) return null;
    switch (fieldObj.type) {
      case "select":
        return (
          <FormControl fullWidth>
            <InputLabel sx={{ color: "#9ca3af" }}>{fieldObj.label}</InputLabel>
            <Select
              value={fieldValue}
              label={fieldObj.label}
              onChange={e => setFieldValue(e.target.value)}
              sx={{ color: "#e0e7ff" }}
            >
              {fieldObj.options.map(opt =>
                <MenuItem key={opt} value={opt}>{opt}</MenuItem>
              )}
            </Select>
          </FormControl>
        );
      case "number":
        return (
          <TextField
            type="number"
            label={fieldObj.label}
            value={fieldValue}
            onChange={e => setFieldValue(e.target.value)}
            fullWidth
            variant="filled"
            InputProps={{ style: { color: '#e0e7ff' } }}
            InputLabelProps={{ style: { color: '#9ca3af' } }}
          />
        );
      case "checkbox":
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={Boolean(fieldValue)}
                onChange={e => setFieldValue(e.target.checked)}
                sx={{ color: "#e0e7ff" }}
              />
            }
            label={fieldObj.label}
          />
        );
      default:
        return (
          <TextField
            label={fieldObj.label}
            value={fieldValue}
            onChange={e => setFieldValue(e.target.value)}
            fullWidth
            variant="filled"
            InputProps={{ style: { color: '#e0e7ff' } }}
            InputLabelProps={{ style: { color: '#9ca3af' } }}
          />
        );
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth
      PaperProps={{
        sx: {
          bgcolor: "#10162a",
          color: "#e0e7ff",
          borderRadius: 3,
          border: "2px solid #334155",
          boxShadow: "0 8px 32px 0 #1e293b88"
        }
      }}>
      <DialogTitle sx={{
        bgcolor: "#181f36", color: "#a5b4fc", borderBottom: "1px solid #334155",
        fontWeight: 700, fontSize: "1.2rem", letterSpacing: 1, pb: 1, px: 4
      }}>
        Mass Update Suppliers
      </DialogTitle>
      <DialogContent sx={{ bgcolor: "#10162a", color: "#e0e7ff", px: 4, pt: 2, pb: 2 }}>
        <Typography sx={{ color: "#4ade80", mb: 2, fontWeight: 600 }}>
          You have selected {selectedCount} supplier(s). Choose field(s) and value(s) to update all selected.
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center" mb={2}>
          <FormControl fullWidth>
            <InputLabel sx={{ color: "#9ca3af" }}>Field</InputLabel>
            <Select
              value={selectedField}
              label="Field"
              onChange={e => setSelectedField(e.target.value)}
              sx={{ color: "#e0e7ff" }}
            >
              {FIELD_OPTIONS.map(opt =>
                <MenuItem key={opt.key} value={opt.key}>{opt.label}</MenuItem>
              )}
            </Select>
          </FormControl>
          <Box sx={{ minWidth: 150 }}>
            {renderFieldInput(FIELD_OPTIONS.find(f => f.key === selectedField))}
          </Box>
          <Button
            variant="outlined"
            onClick={handleAddField}
            sx={{
              color: "#a5b4fc",
              borderColor: "#3730a3",
              height: "40px",
              fontWeight: 600,
              ml: 1
            }}
            disabled={!selectedField}
          >
            Add
          </Button>
        </Stack>
        <Box>
          {batchFields.length > 0 && (
            <Box className="space-y-2 mb-2">
              {batchFields.map((f, i) => (
                <Box key={i} className="flex items-center justify-between bg-gray-800 rounded px-3 py-2">
                  <Typography>{f.label}: <span className="font-mono">{String(f.value)}</span></Typography>
                  <Button onClick={() => handleRemoveField(i)} color="error" size="small">Remove</Button>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{
        bgcolor: "#181f36", borderTop: "1px solid #334155", py: 2, px: 4, gap: 2,
        display: "flex", flexWrap: "wrap", justifyContent: "flex-end"
      }}>
        <Button onClick={onClose}
          sx={{
            bgcolor: "#334155", color: "#e0e7ff", borderRadius: 2, fontWeight: 600,
            mb: 1, "&:hover": { bgcolor: "#475569" }
          }}>
          Cancel
        </Button>
        <Button
          onClick={handleApply}
          variant="contained"
          sx={{
            bgcolor: "#6366f1", color: "#e0e7ff", fontWeight: 600, borderRadius: 2,
            boxShadow: "0 0 8px 0 #6366f1bb", mr: 1, mb: 1, "&:hover": { bgcolor: "#818cf8" }
          }}
          disabled={batchFields.length === 0}
        >
          Apply to All Selected
        </Button>
      </DialogActions>
    </Dialog>
  );
}
