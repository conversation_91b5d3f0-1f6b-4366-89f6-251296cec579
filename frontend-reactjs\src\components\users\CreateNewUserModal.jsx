import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
} from "@mui/material";
import toast from "react-hot-toast";
import API from "../../services/api";

export default function CreateNewUserModal({ open, onClose, onCreated, roles }) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [selectedRoles, setSelectedRoles] = useState([]);

  // Reset form when modal opens
  useEffect(() => {
    if (open) {
      setUsername("");
      setPassword("");
      setSelectedRoles([]);
    }
  }, [open]);

  const handleCreate = async () => {
    if (!username || !password || selectedRoles.length === 0) {
      toast.error("Please fill in all fields.");
      return;
    }

    try {
      await API.post("/users", {
        username,
        password,
        roles: selectedRoles,
      });
      toast.success("User created");
      onCreated();
      onClose();
    } catch (err) {
      // Pydantic may return detail as an array of error objects
      const detail = err.response?.data?.detail;
      let errorMsg = err.message;
      if (detail) {
        if (Array.isArray(detail)) {
          errorMsg = detail
            .map((e) =>
              Array.isArray(e.loc)
                ? `${e.loc.join('.')}: ${e.msg}`
                : `${e.loc}: ${e.msg}`
            )
            .join(", ");
        } else if (typeof detail === 'string') {
          errorMsg = detail;
        }
      }
      console.error("Create user failed", errorMsg);
      toast.error(errorMsg);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>Create New User</DialogTitle>
      <DialogContent dividers>
        <TextField
          margin="dense"
          label="Username"
          type="text"
          fullWidth
          value={username}
          onChange={(e) => setUsername(e.target.value)}
        />
        <TextField
          margin="dense"
          label="Password"
          type="password"
          fullWidth
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <FormControl autoComplete="off" fullWidth margin="dense">
          <InputLabel id="roles-label">Roles</InputLabel>
          <Select
            labelId="roles-label"
            multiple
            value={selectedRoles}
            onChange={(e) => setSelectedRoles(e.target.value)}
            renderValue={(selected) =>
              selected
                .map((r) => r.charAt(0).toUpperCase() + r.slice(1))
                .join(", ")
            }
            label="Roles"
          >
            {roles.map((roleName) => (
              <MenuItem key={roleName} value={roleName}>
                <Checkbox checked={selectedRoles.includes(roleName)} />
                <ListItemText
                  primary={
                    roleName.charAt(0).toUpperCase() + roleName.slice(1)
                  }
                />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button onClick={handleCreate} variant="contained" color="primary">
          Create
        </Button>
      </DialogActions>
    </Dialog>
  );
}
