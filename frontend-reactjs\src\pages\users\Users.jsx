import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import API from "../../services/api";
import UserTable from "../../components/users/UserTable";
import toast from "react-hot-toast";

export default function UsersPage() {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchUsers = async () => {
    try {
      const res = await API.get("/users");
      setUsers(res.data);
    } catch (err) {
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleDelete = async (userId) => {
    try {
      await API.delete(`/users/${userId}`);
      toast.success("User deleted");
      fetchUsers();
    } catch {
      toast.error("Delete failed");
    }
  };

  const cards = [
    {
      key: "back",
      title: "Go Back",
      desc: "Return to the previous page.",
      action: () => navigate(-1),
    },
    {
      key: "permissions",
      title: "Permissions",
      desc: "Control role-based access to different modules.",
      route: "/permissions",
    },
    {
      key: "logs",
      title: "Logs",
      desc: "View recent system activity and change logs.",
      route: "/logs",
    },
  ];

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white">
      {/* <Navbar /> */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-semibold mb-6 text-white">
          User Management
        </h1>

        {loading ? (
          <div className="text-purple-400 animate-pulse">
            Loading users...
          </div>
        ) : (
          <>
            <UserTable
              users={users}
              onDelete={handleDelete}
              onCreated={fetchUsers}
            />

            <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
              {cards.map(({ key, title, desc, action, route }) => (
                <div
                  key={key}
                  onClick={() => (action ? action() : navigate(route))}
                  className="bg-gray-900 border border-gray-700 rounded-xl shadow-lg p-6 flex flex-col justify-start h-full min-h-[220px] transition-all duration-200 ease-in-out cursor-pointer transform hover:scale-[1.02] hover:bg-gray-800 hover:border-purple-500"
                >
                  <h2 className="text-xl font-bold text-white mb-2">
                    {title}
                  </h2>
                  <p className="text-gray-400 text-sm">{desc}</p>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
