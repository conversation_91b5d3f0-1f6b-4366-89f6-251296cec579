import React, { createContext, useContext, useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import API from "../../services/api";
import toast from "react-hot-toast";

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState({
    username: null,
    roles: [],
    last_login: null,
  });

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      if (location.pathname === "/login") {
        setLoading(false);
        return;
      }
      try {
        const res = await API.get("/auth/validate", { withCredentials: true });
        setIsAuthenticated(true);
        setUser({
          username: res.data.username,
          roles: res.data.roles,
          last_login: res.data.last_login,
        });
      } catch {
        setIsAuthenticated(false);
        setUser({ username: null, roles: [], last_login: null });
        if (location.pathname !== "/login") {
          toast.error("Browser session has expired. Please log in again.");
          navigate("/login");
        }
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [location.pathname, navigate]);

  // Auto‐refresh token every 5 minutes
  useEffect(() => {
    let interval;
    if (isAuthenticated) {
      interval = setInterval(async () => {
        try {
          await API.post("/auth/refresh", null, { withCredentials: true });
        } catch {
          console.error("Failed to refresh token");
          setIsAuthenticated(false);
          setUser({ username: null, roles: [], last_login: null });
          if (location.pathname !== "/login") {
            toast.error("Session expired. Please login again.");
            navigate("/login");
          }
        }
      }, 5 * 60 * 1000);
    }
    return () => clearInterval(interval);
  }, [isAuthenticated, location.pathname, navigate]);

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, loading, user, setIsAuthenticated, setUser }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}