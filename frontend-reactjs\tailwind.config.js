const scrollbar = require('tailwind-scrollbar');

module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './node_modules/tw-elements-react/dist/js/**/*.js'
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#2563eb',

        },
        // add more colors here (success, danger, warning, info, etc) as needed
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      boxShadow: {
        glow: '0 0 0 2px rgba(37, 99, 235, 0.3), 0 6px 24px rgba(37, 99, 235, 0.2)',
        elevate: '0 8px 20px rgba(0, 0, 0, 0.08)',
      },
    },
  },
  plugins: [
    scrollbar,
  ],
};
