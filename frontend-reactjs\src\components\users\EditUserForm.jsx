import React from "react";

export default function EditUserForm({
  selectedUser,
  updatedUser,
  roles,
  onChange,
  onSubmit,
  onCancel,
}) {
  return (
    <form autoComplete="off" onSubmit={onSubmit} className="bg-white/10 backdrop-blur-md p-6 rounded-2xl shadow-md text-white space-y-6">
      <h3 className="text-xl font-semibold">Edit: {selectedUser.username}</h3>

      <div>
        <label className="block mb-1 text-sm">Role</label>
        <select
          name="role"
          value={updatedUser.role}
          onChange={onChange}
          className="w-full bg-black text-white border border-gray-600 rounded px-4 py-2"
        >
          {roles.map((role) => (
            <option key={role} value={role}>{role}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block mb-1 text-sm">New Password (optional)</label>
        <input
          type="password"
          name="password"
          value={updatedUser.password}
          onChange={onChange}
          className="w-full bg-black text-white border border-gray-600 rounded px-4 py-2"
        />
      </div>

      <div className="flex justify-end gap-4">
        <button type="button" onClick={onCancel} className="px-4 py-2 bg-gray-600 rounded-full hover:bg-gray-500">Cancel</button>
        <button type="submit" className="px-4 py-2 bg-blue-500 rounded-full hover:bg-blue-400 text-white">Save</button>
      </div>
    </form>
  );
}
