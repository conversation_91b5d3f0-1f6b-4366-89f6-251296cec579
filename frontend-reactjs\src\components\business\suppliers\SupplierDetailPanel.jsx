// SupplierDetailPanel.jsx
import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  ToggleButton,
  ToggleButtonGroup,
  Autocomplete,
  Paper,
  Popper,
} from "@mui/material";
import { Add, Delete, UploadFile } from "@mui/icons-material";
import API from "../../../services/api";
import SupplierContractsTable from "./SupplierContractsTable";
import toast from "react-hot-toast";

// Helper: display value or “—”
const D = (val) => val ?? "—";

// --- Shared field wrapper (for most inputs) ---
const StyledField = ({ children, minHeight = 46 }) => (
  <Box
    sx={{
      bgcolor: "#1e293b",
      px: 2,
      py: 1,
      borderRadius: 1,
      border: "1px solid #232949",
      width: "100%",
      minHeight,
      display: "flex",
      alignItems: "center",
    }}
  >
    {children}
  </Box>
);

// --- Common container for all tab panels ---
// Note: this also has the gradient background & border. We’ll leave it here unchanged.
const PanelContainer = ({ children }) => (
  <Box
    sx={{
      background: "linear-gradient(135deg,#181f36 85%,#0f172a 100%)",
      borderRadius: 1,
      border: "1.5px solid #282b44",
      boxShadow: "0 6px 48px #181f36cc",
      p: 0,
      mb: 2,
      overflow: "auto",
      minHeight: 180,
      maxHeight: 345,
    }}
    className="scrollbar-thin"
  >
    <Box sx={{ px: 3, py: 3 }}>{children}</Box>
  </Box>
);

export default function SupplierDetailPanel({
  supplier,
  onClose,
  onSuccess,
  style,
}) {
  const [mode, setMode] = useState("view");
  const [tabIndex, setTabIndex] = useState(0);
  const [form, setForm] = useState({
    company_name: "",
    company_code: "",
    notes: "",
    addresses: [
      {
        type: "billing",
        street_address: "",
        city: "",
        state: "",
        zipcode: "",
        phone: "",
        contact_email: "",
        notes: "",
        is_primary: true,
      },
      {
        type: "shipping",
        street_address: "",
        city: "",
        state: "",
        zipcode: "",
        phone: "",
        contact_email: "",
        notes: "",
        is_primary: true,
      },
    ],
    contacts: [{ name: "", email: "", phone: "", is_primary: true, title: "" }],
    documents: [],
    profile: {
      payment_terms: "",
      credit_limit: "",
      credit_hold: false,
      sla_description: "",
      effective_date: "",
      expiration_date: "",
    },
  });
  const [pendingDocuments, setPendingDocuments] = useState([]);
  const [contractFile, setContractFile] = useState(null);
  const docInputRef = useRef(null);
  const contractInputRef = useRef(null);

  const paymentTermsOptions = [
    "Net 15",
    "Net 30",
    "Net 60",
    "Net 90",
    "Due on Receipt",
  ];

  //— load supplier into form when prop changes —//
  useEffect(() => {
    if (supplier) {
      setForm({
        id: supplier.id,
        company_name: supplier.company_name ?? "",
        company_code: supplier.company_code ?? "",
        notes: supplier.notes ?? "",
        addresses: supplier.addresses?.length
          ? supplier.addresses.map((addr) => ({
              type: addr.type ?? "",
              street_address: addr.street_address ?? "",
              city: addr.city ?? "",
              state: addr.state ?? "",
              zipcode: addr.zipcode ?? "",
              phone: addr.phone ?? "",
              contact_email: addr.contact_email ?? "",
              notes: addr.notes ?? "",
              is_primary: addr.is_primary ?? false,
            }))
          : [
              {
                type: "billing",
                street_address: "",
                city: "",
                state: "",
                zipcode: "",
                phone: "",
                contact_email: "",
                notes: "",
                is_primary: true,
              },
              {
                type: "shipping",
                street_address: "",
                city: "",
                state: "",
                zipcode: "",
                phone: "",
                contact_email: "",
                notes: "",
                is_primary: true,
              },
            ],
        contacts: supplier.contacts?.length
          ? supplier.contacts.map((contact) => ({
              name: contact.name ?? "",
              email: contact.email ?? "",
              phone: contact.phone ?? "",
              is_primary: contact.is_primary ?? false,
              title: contact.title ?? "",
            }))
          : [{ name: "", email: "", phone: "", is_primary: true, title: "" }],
        documents: supplier.documents ?? [],
        profile: {
          payment_terms: supplier.profile?.payment_terms ?? "",
          credit_limit: supplier.profile?.credit_limit ?? "",
          credit_hold: supplier.profile?.credit_hold ?? false,
          sla_description: supplier.profile?.sla_description ?? "",
          effective_date: supplier.profile?.effective_date ?? "",
          expiration_date: supplier.profile?.expiration_date ?? "",
        },
      });
    }
    setTabIndex(0);
    setMode("view");
    setContractFile(null);
    setPendingDocuments([]);
    if (docInputRef.current) docInputRef.current.value = null;
    if (contractInputRef.current) contractInputRef.current.value = null;
  }, [supplier]);

  const updateField = (key, value) => {
    if (key.startsWith("profile.")) {
      const pKey = key.split(".")[1];
      setForm((prev) => ({
        ...prev,
        profile: { ...prev.profile, [pKey]: value },
      }));
    } else {
      setForm((prev) => ({ ...prev, [key]: value }));
    }
  };

  const handleTabChange = (_, newValue) => setTabIndex(newValue);

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files || []);
    setPendingDocuments((prev) => [...prev, ...files]);
  };

  const handlePendingFileDelete = (filename) => {
    setPendingDocuments((prev) =>
      prev.filter((file) => file.name !== filename)
    );
    if (docInputRef.current) docInputRef.current.value = null;
  };

  const handleContractFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setContractFile(file);
    }
  };

  const handlePendingContractDelete = () => {
    setContractFile(null);
    if (contractInputRef.current) contractInputRef.current.value = null;
  };

  const handleContractEdit = () => toast("Replace/Edit contract not implemented yet.");

  const handleContractDelete = async (contract) => {
    if (!contract || !supplier?.id) return;
    if (!window.confirm("Are you sure you want to delete this contract file?"))
      return;
    try {
      await API.delete(`/suppliers/${supplier.id}/contracts/${contract.id}`);
      setForm((prev) => ({
        ...prev,
        documents: prev.documents.filter((d) => d.id !== contract.id),
      }));
      toast.success("Contract deleted");
    } catch {
      toast.error("Failed to delete contract");
    }
  };

  const handleSetPrimaryAddress = (idx) => {
    const t = form.addresses[idx].type;
    const updated = form.addresses.map((addr, i) => ({
      ...addr,
      is_primary: i === idx ? true : addr.type === t ? false : addr.is_primary,
    }));
    updateField("addresses", updated);
  };

  const handleAddAddress = (type) => {
    const isPrimary =
      form.addresses.filter((a) => a.type === type).length === 0;
    updateField("addresses", [
      ...form.addresses,
      {
        type,
        street_address: "",
        city: "",
        state: "",
        zipcode: "",
        phone: "",
        contact_email: "",
        notes: "",
        is_primary: isPrimary,
      },
    ]);
  };

  const handleDeleteAddress = (i) => {
    const toDelete = form.addresses[i];
    const updated = form.addresses.filter((_, idx) => idx !== i);
    if (toDelete.is_primary) {
      const sameType = updated.filter((a) => a.type === toDelete.type);
      if (sameType.length > 0) {
        const idx0 = updated.indexOf(sameType[0]);
        updated[idx0] = { ...updated[idx0], is_primary: true };
      }
    }
    updateField("addresses", updated);
  };

  const isValid = form.company_name.trim().length > 0;

  const handleSubmit = async () => {
    try {
      const cleanProfile = (p) => ({
        ...p,
        credit_limit:
          p.credit_limit === "" || p.credit_limit === null
            ? null
            : Number(p.credit_limit),
        effective_date: p.effective_date || null,
        expiration_date: p.expiration_date || null,
        payment_terms: p.payment_terms || null,
      });

      const docsWithType = (form.documents || []).map((d) => ({
        ...d,
        type: d.type ?? "document",
      }));

      const payload = {
        ...form,
        documents: docsWithType,
        profile: cleanProfile(form.profile),
      };

      const method = form.id ? API.put : API.post;
      const url = form.id ? `/suppliers/${form.id}` : "/suppliers";
      const response = await method(url, payload);

      const supplierId = form.id || response.data?.id || response.id;

      // 1) upload contract if pending
      if (contractFile && supplierId) {
        const fd = new FormData();
        fd.append("file", contractFile);
        const respC = await API.post(
          `/suppliers/${supplierId}/contracts`,
          fd,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        setForm((prev) => ({
          ...prev,
          documents: [
            ...prev.documents,
            {
              id: respC.data.id,
              filename: contractFile.name,
              file_url: respC.data.file_url,
              uploaded_at: new Date().toISOString(),
              type: "contract",
            },
          ],
        }));
        setContractFile(null);
        if (contractInputRef.current) contractInputRef.current.value = null;
      }

      // 2) upload pending documents
      if (pendingDocuments.length && supplierId) {
        for (const file of pendingDocuments) {
          const fd = new FormData();
          fd.append("file", file);
          const respD = await API.post(
            `/suppliers/${supplierId}/documents`,
            fd,
            { headers: { "Content-Type": "multipart/form-data" } }
          );
          setForm((prev) => ({
            ...prev,
            documents: [
              ...prev.documents,
              {
                id: respD.data.id,
                filename: file.name,
                file_url: respD.data.file_url,
                uploaded_at: new Date().toISOString(),
                type: "document",
              },
            ],
          }));
        }
        setPendingDocuments([]);
        if (docInputRef.current) docInputRef.current.value = null;
      }

      toast.success(`Supplier ${form.id ? "updated" : "created"}`);
      setMode("view");
      onSuccess && onSuccess();
    } catch (err) {
      console.error("Save failed:", err.response?.data || err.message);
      toast.error("Save failed");
    }
  };

  const handleDeleteDocument = async (docId) => {
    if (!supplier?.id) return;
    if (!window.confirm("Delete this document?")) return;
    try {
      await API.delete(`/suppliers/${supplier.id}/documents/${docId}`);
      setForm((prev) => ({
        ...prev,
        documents: prev.documents.filter((d) => d.id !== docId),
      }));
      toast.success("Document deleted");
    } catch (err) {
      console.error("Failed to delete document:", err.response?.data || err);
      toast.error("Failed to delete document");
    }
  };

  return (
    <Box
      sx={{
        // ─── 1) THIS BOX IS YOUR “invisible spacer” ─────────
        //
        //    We push down the entire panel by 64px. Because
        //    this <Box> has `bgcolor: "transparent"`, that 64px
        //    will be see-through, showing whatever is behind it.
        //
        mt: "64px",
        bgcolor: "transparent",     // ensure the gap is truly transparent
        p: 0,
        m: 0,
        minWidth: 0,
        ...style,
      }}
    >
      {/* ─── 2) NOW RENDER YOUR ACTUAL “PANEL” BELOW ─────── */}
      <Box
        sx={{
          bgcolor: "transparent",   // keep top‐level transparent here as well
          color: "#e0e7ff",
          minHeight: 300,
          maxHeight: 300,
          borderRadius: 2,
          border: "1.5px solid #232949",
          boxShadow: "none",
          p: 0,
          pt: 1,
          overflow: "visible",
        }}
      >
        {/* — Top Row: Title + Tabs + Actions — */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 2,
            px: 2,
            borderBottom: "1.5px solid #232949",
            minHeight: 42,
          }}
        >
          {mode === "edit" ? (
            <>
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
                size="small"
                disabled={!isValid}
                sx={{ mr: 1 }}
              >
                {form.id ? "Update" : "Create Supplier"}
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                onClick={() => setMode("view")}
                sx={{ mr: 2 }}
              >
                Cancel
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="contained"
                color="primary"
                size="small"
                sx={{ mr: 1 }}
                onClick={() => setMode("edit")}
              >
                Edit
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                sx={{ mr: 2 }}
                onClick={onClose}
              >
                Close
              </Button>
            </>
          )}
          <Tabs
            value={tabIndex}
            onChange={handleTabChange}
            textColor="secondary"
            indicatorColor="secondary"
            sx={{ flex: 1, minHeight: 40 }}
          >
            <Tab label="General" />
            <Tab label="Addresses" />
            <Tab label="Contacts" />
            <Tab label="Profile" />
            <Tab label="Documents" />
          </Tabs>
        </Box>

        {/* ─── 3) TABS CONTENT (unchanged) ───────────────── */}
        {tabIndex === 0 && (
          <PanelContainer>
            <Box
              sx={{
                display: "flex",
                gap: 3,
                alignItems: "flex-start",
                flexWrap: "wrap",
              }}
            >
              <Box sx={{ flex: 1, minWidth: 220 }}>
                <Typography
                  sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}
                >
                  Supplier Name
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <TextField
                      label="Company Name"
                      value={form.company_name}
                      onChange={(e) =>
                        updateField("company_name", e.target.value)
                      }
                      fullWidth
                      required
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 18 }}>
                      {D(form.company_name)}
                    </Typography>
                  )}
                </StyledField>
              </Box>
              <Box sx={{ flex: 1, minWidth: 180 }}>
              <Typography
                  sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}
                >
                  Supplier Code
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <TextField
                      label="Company Code"
                      value={form.company_code}
                      onChange={(e) =>
                        updateField("company_code", e.target.value)
                      }
                      fullWidth
                      required
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 18 }}>
                      {D(form.company_code)}
                    </Typography>
                  )}
                </StyledField>
              </Box>
            </Box>
            <Box sx={{ my: 2 }}>
              <Typography
                sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}
              >
                Notes
              </Typography>
              <StyledField>
                {mode === "edit" ? (
                  <TextField
                    label="Notes"
                    value={form.notes}
                    onChange={(e) => updateField("notes", e.target.value)}
                    multiline
                    fullWidth
                    minRows={2}
                    variant="standard"
                    InputProps={{
                      disableUnderline: true,
                      style: { color: "#e0e7ff" },
                    }}
                  />
                ) : (
                  <Typography sx={{ fontSize: 15 }}>
                    {D(form.notes)}
                  </Typography>
                )}
              </StyledField>
            </Box>
          </PanelContainer>
        )}

        {tabIndex === 1 && (
          <PanelContainer>
            <Box sx={{ mb: 2, display: "flex", gap: 2 }}>
              <Button
                startIcon={<Add />}
                size="small"
                variant="outlined"
                disabled={mode !== "edit"}
                onClick={() => handleAddAddress("billing")}
              >
                Add Billing Address
              </Button>
              <Button
                startIcon={<Add />}
                size="small"
                variant="outlined"
                disabled={mode !== "edit"}
                onClick={() => handleAddAddress("shipping")}
              >
                Add Shipping Address
              </Button>
            </Box>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Street</TableCell>
                    <TableCell>City</TableCell>
                    <TableCell>State</TableCell>
                    <TableCell>Zip</TableCell>
                    <TableCell>Phone</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Notes</TableCell>
                    <TableCell>Primary</TableCell>
                    {mode === "edit" && <TableCell />}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {form.addresses.map((address, i) => (
                    <TableRow key={i}>
                      <TableCell>{address.type}</TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.street_address}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].street_address = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.street_address)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.city}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].city = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.city)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.state}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].state = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.state)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.zipcode}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].zipcode = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.zipcode)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.phone}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].phone = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.phone)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.contact_email}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].contact_email = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.contact_email)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={address.notes}
                              onChange={(e) => {
                                const updated = [...form.addresses];
                                updated[i].notes = e.target.value;
                                updateField("addresses", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(address.notes)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={address.is_primary}
                                  onChange={() => handleSetPrimaryAddress(i)}
                                  color="primary"
                                />
                              }
                              label=""
                            />
                          ) : address.is_primary ? (
                            "Yes"
                          ) : (
                            ""
                          )}
                        </StyledField>
                      </TableCell>
                      {mode === "edit" && (
                        <TableCell>
                          <Button
                            size="small"
                            onClick={() => handleDeleteAddress(i)}
                            color="secondary"
                          >
                            <Delete fontSize="small" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </PanelContainer>
        )}

        {tabIndex === 2 && (
          <PanelContainer>
            <Button
              startIcon={<Add />}
              size="small"
              variant="outlined"
              disabled={mode !== "edit"}
              sx={{ mb: 2, mt: 1 }}
              onClick={() =>
                updateField("contacts", [
                  ...form.contacts,
                  { name: "", email: "", phone: "", is_primary: false, title: "" },
                ])
              }
            >
              Add Contact
            </Button>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Phone</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Primary</TableCell>
                    {mode === "edit" && <TableCell />}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {form.contacts.map((contact, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={contact.name}
                              onChange={(e) => {
                                const updated = [...form.contacts];
                                updated[i].name = e.target.value;
                                updateField("contacts", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(contact.name)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={contact.email}
                              onChange={(e) => {
                                const updated = [...form.contacts];
                                updated[i].email = e.target.value;
                                updateField("contacts", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(contact.email)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={contact.phone}
                              onChange={(e) => {
                                const updated = [...form.contacts];
                                updated[i].phone = e.target.value;
                                updateField("contacts", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(contact.phone)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <TextField
                              value={contact.title}
                              onChange={(e) => {
                                const updated = [...form.contacts];
                                updated[i].title = e.target.value;
                                updateField("contacts", updated);
                              }}
                              variant="standard"
                              InputProps={{
                                disableUnderline: true,
                                style: { color: "#e0e7ff" },
                              }}
                            />
                          ) : (
                            D(contact.title)
                          )}
                        </StyledField>
                      </TableCell>
                      <TableCell>
                        <StyledField>
                          {mode === "edit" ? (
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={contact.is_primary}
                                  onChange={() => {
                                    const updated = form.contacts.map((c, idx) => ({
                                      ...c,
                                      is_primary: idx === i,
                                    }));
                                    updateField("contacts", updated);
                                  }}
                                  color="primary"
                                />
                              }
                              label=""
                            />
                          ) : contact.is_primary ? (
                            "Yes"
                          ) : (
                            ""
                          )}
                        </StyledField>
                      </TableCell>
                      {mode === "edit" && (
                        <TableCell>
                          <Button
                            size="small"
                            color="secondary"
                            onClick={() =>
                              updateField(
                                "contacts",
                                form.contacts.filter((_, idx) => idx !== i)
                              )
                            }
                          >
                            <Delete fontSize="small" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </PanelContainer>
        )}

        {tabIndex === 3 && (
          <PanelContainer>
            {/* Row 1: Payment Terms + Credit Limit */}
            <Box sx={{ display: "flex", gap: 3, mb: 3, flexWrap: "wrap" }}>
              {/* Payment Terms (ToggleButtonGroup) */}
              <Box sx={{ flex: 1, minWidth: 200 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  Payment Terms
                </Typography>

                <StyledField
                  sx={{
                    // shrink padding so the toggle buttons fill the field nicely
                    px: 0,
                    py: 0,
                    alignItems: "center",
                  }}
                >
                  {mode === "edit" ? (
                    <ToggleButtonGroup
                      value={form.profile.payment_terms}
                      exclusive
                      onChange={(_, newVal) => {
                        // newVal is one of the strings or null
                        updateField("profile.payment_terms", newVal || "");
                      }}
                      aria-label="payment-terms"
                      sx={{
                        width: "100%",
                      }}
                    >
                      <ToggleButton
                        value=""
                        aria-label="none"
                        sx={{
                          flex: 1,
                          color:
                            form.profile.payment_terms === ""
                              ? "#0f172a"
                              : "#e0e7ff",
                          bgcolor:
                            form.profile.payment_terms === ""
                              ? "#a5b4fc"
                              : "transparent",
                          borderColor: "#232949",
                          textTransform: "none",
                          "&.Mui-selected": {
                            bgcolor: "#a5b4fc",
                            color: "#0f172a",
                          },
                        }}
                      >
                        None
                      </ToggleButton>

                      {paymentTermsOptions.map((term) => (
                        <ToggleButton
                          key={term}
                          value={term}
                          aria-label={term}
                          sx={{
                            flex: 1,
                            color:
                              form.profile.payment_terms === term
                                ? "#0f172a"
                                : "#e0e7ff",
                            bgcolor:
                              form.profile.payment_terms === term
                                ? "#a5b4fc"
                                : "transparent",
                            borderColor: "#232949",
                            textTransform: "none",
                            "&.Mui-selected": {
                              bgcolor: "#a5b4fc",
                              color: "#0f172a",
                            },
                          }}
                        >
                          {term}
                        </ToggleButton>
                      ))}
                    </ToggleButtonGroup>
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {D(form.profile.payment_terms)}
                    </Typography>
                  )}
                </StyledField>
              </Box>

              {/* Credit Limit (unchanged) */}
              <Box sx={{ flex: 1, minWidth: 120 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  Credit Limit
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <TextField
                      value={form.profile.credit_limit}
                      onChange={(e) =>
                        updateField("profile.credit_limit", e.target.value)
                      }
                      type="number"
                      fullWidth
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {D(form.profile.credit_limit)}
                    </Typography>
                  )}
                </StyledField>
              </Box>
            </Box>

            {/* Row 2: Credit Hold + SLA Description */}
            <Box sx={{ display: "flex", gap: 3, mb: 3, flexWrap: "wrap" }}>
              {/* Credit Hold */}
              <Box sx={{ minWidth: 150 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  Credit Hold
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={!!form.profile.credit_hold}
                          onChange={(e) =>
                            updateField("profile.credit_hold", e.target.checked)
                          }
                          color="primary"
                        />
                      }
                      label=""
                      sx={{ m: 0 }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {form.profile.credit_hold ? "Yes" : "No"}
                    </Typography>
                  )}
                </StyledField>
              </Box>

              {/* SLA Description */}
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  SLA Description
                </Typography>
                <StyledField sx={{ alignItems: "flex-start", minHeight: 80 }}>
                  {mode === "edit" ? (
                    <TextField
                      value={form.profile.sla_description || ""}
                      onChange={(e) =>
                        updateField("profile.sla_description", e.target.value)
                      }
                      fullWidth
                      multiline
                      minRows={2}
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {D(form.profile.sla_description)}
                    </Typography>
                  )}
                </StyledField>
              </Box>
            </Box>

            {/* Row 3: Effective Date + Expiration Date */}
            <Box sx={{ display: "flex", gap: 3, mb: 2, flexWrap: "wrap" }}>
              {/* Effective Date */}
              <Box sx={{ flex: 1, minWidth: 200 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  Effective Date
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <TextField
                      value={form.profile.effective_date || ""}
                      onChange={(e) =>
                        updateField("profile.effective_date", e.target.value)
                      }
                      type="date"
                      fullWidth
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {form.profile.effective_date
                        ? new Date(form.profile.effective_date).toLocaleDateString()
                        : "—"}
                    </Typography>
                  )}
                </StyledField>
              </Box>

              {/* Expiration Date */}
              <Box sx={{ flex: 1, minWidth: 200 }}>
                <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                  Expiration Date
                </Typography>
                <StyledField>
                  {mode === "edit" ? (
                    <TextField
                      value={form.profile.expiration_date || ""}
                      onChange={(e) =>
                        updateField("profile.expiration_date", e.target.value)
                      }
                      type="date"
                      fullWidth
                      variant="standard"
                      InputProps={{
                        disableUnderline: true,
                        style: { color: "#e0e7ff" },
                      }}
                    />
                  ) : (
                    <Typography sx={{ fontSize: 15 }}>
                      {form.profile.expiration_date
                        ? new Date(form.profile.expiration_date).toLocaleDateString()
                        : "—"}
                    </Typography>
                  )}
                </StyledField>
              </Box>
            </Box>
          </PanelContainer>
        )}

        {tabIndex === 4 && (
          <PanelContainer>
            {/* 1) Upload buttons */}
            <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
              <Button
                component="label"
                variant="outlined"
                startIcon={<UploadFile />}
                disabled={mode !== "edit"}
              >
                Upload Document(s)
                <input
                  type="file"
                  multiple
                  hidden
                  onChange={handleFileChange}
                  ref={docInputRef}
                />
              </Button>
              <Button
                component="label"
                variant="outlined"
                startIcon={<UploadFile />}
                disabled={mode !== "edit"}
              >
                Upload Contract
                <input
                  type="file"
                  accept="*/*"
                  hidden
                  onChange={handleContractFileChange}
                  ref={contractInputRef}
                />
              </Button>
            </Box>

            {/* 2) Pending uploads */}
            {mode === "edit" && (
              <Box sx={{ mb: 2 }}>
                {pendingDocuments.length > 0 && (
                  <Box sx={{ mb: 1 }}>
                    <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mb: 1 }}>
                      Pending Documents
                    </Typography>
                    <ul>
                      {pendingDocuments.map((file, idx) => (
                        <li
                          key={file.name + idx}
                          style={{ display: "flex", alignItems: "center" }}
                        >
                          <span>{file.name}</span>
                          <Button
                            size="small"
                            color="secondary"
                            onClick={() => handlePendingFileDelete(file.name)}
                          >
                            <Delete fontSize="small" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </Box>
                )}
                {contractFile && (
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography sx={{ fontWeight: 600, color: "#a5b4fc", mr: 1 }}>
                      Pending Contract:
                    </Typography>
                    <span>{contractFile.name}</span>
                    <Button
                      size="small"
                      color="secondary"
                      onClick={handlePendingContractDelete}
                      sx={{ ml: 1 }}
                    >
                      <Delete fontSize="small" />
                    </Button>
                  </Box>
                )}
              </Box>
            )}

            {/* 3) Documents table */}
            {form.documents?.filter((doc) => doc.type !== "contract").length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography sx={{ color: "#a5b4fc", fontWeight: 600, mb: 1 }}>
                  Documents
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Filename</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Uploaded At</TableCell>
                        {mode === "edit" && <TableCell />}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {form.documents
                        .filter((doc) => doc.type !== "contract")
                        .map((doc) => (
                          <TableRow key={doc.id}>
                            <TableCell>
                              <a
                                href={doc.file_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  color: "#e0e7ff",
                                  textDecoration: "underline",
                                }}
                              >
                                {doc.filename || "—"}
                              </a>
                            </TableCell>
                            <TableCell>{doc.type}</TableCell>
                            <TableCell>
                              {doc.uploaded_at
                                ? new Date(doc.uploaded_at).toLocaleDateString()
                                : "—"}
                            </TableCell>
                            {mode === "edit" && (
                              <TableCell>
                                <Button
                                  size="small"
                                  color="secondary"
                                  onClick={() => handleDeleteDocument(doc.id)}
                                >
                                  <Delete fontSize="small" />
                                </Button>
                              </TableCell>
                            )}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* 4) Contracts table */}
            {form.documents?.filter((doc) => doc.type === "contract").length > 0 && (
              <Box>
                <Typography sx={{ color: "#a5b4fc", fontWeight: 600, mb: 1 }}>
                  Contracts
                </Typography>
                <SupplierContractsTable
                  documents={form.documents.filter((doc) => doc.type === "contract")}
                  onEdit={handleContractEdit}
                  onDelete={handleContractDelete}
                  supplier={supplier}
                />
              </Box>
            )}
          </PanelContainer>
        )}
      </Box>
    </Box>
  );
}
