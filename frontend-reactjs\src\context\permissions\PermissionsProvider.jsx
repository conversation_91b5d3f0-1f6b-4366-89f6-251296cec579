import React, {
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import API from "../../services/api";
import { useAuth } from "../auth/AuthProvider";

const PermissionsContext = createContext({
  perms: [],
  loaded: false,
});

export function PermissionsProvider({ children }) {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [perms, setPerms] = useState([]);
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    let cancelled = false;

    if (authLoading) return;

    const fetchPermissions = async () => {
      if (!isAuthenticated) {
        if (!cancelled) {
          setPerms([]);
          setLoaded(true);
        }
        return;
      }

      setLoaded(false);
      try {
        const res = await API.get("/auth/permissions", {
          withCredentials: true,
        });
        const result = res.data || [];
        if (!cancelled) {
          setPerms(result);
          if (result.length === 0) {
            console.warn(
              "User is authenticated but has 0 permissions."
            );
          }
        }
      } catch (err) {
        if (!cancelled) {
          console.error("Failed to load permissions", err);
          setPerms([]);
        }
      } finally {
        if (!cancelled) setLoaded(true);
      }
    };

    fetchPermissions();

    return () => {
      cancelled = true;
    };
  }, [isAuthenticated, authLoading]);

  return (
    <PermissionsContext.Provider value={{ perms, loaded }}>
      {children}
    </PermissionsContext.Provider>
  );
}

export function usePermissions() {
  return useContext(PermissionsContext);
}
